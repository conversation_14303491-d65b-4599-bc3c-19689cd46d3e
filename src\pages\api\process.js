// Import other utilities and the GPTZero client
import { addControlledMistakes, changeStyle, simpleParaphrase } from '../../utils/textModifiers';
import { checkWithGPTZero } from '../../services/gptzeroClient';
import { paraphraseWithPegasus } from '../../services/paraphraseService'; // Import the new PEGASUS service client

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { text } = req.body;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return res.status(400).json({ message: 'Input text is required and must be a non-empty string.' });
    }

    let modifiedText = text; // Start with the original text

    try {
        // --- Step 1: Advanced Paraphrasing with PEGASUS Microservice (with fallback) ---
        console.log("Calling PEGASUS paraphrasing service...");
        const pegasusResult = await paraphraseWithPegasus(modifiedText);

        if (pegasusResult && !pegasusResult.error && typeof pegasusResult.paraphrased_text === 'string') {
            modifiedText = pegasusResult.paraphrased_text;
            console.log("Successfully paraphrased with PEGASUS service.");
        } else {
            // Fallback to local paraphrasing when PEGASUS service is not available
            console.warn(`PEGASUS paraphrasing service call failed: ${pegasusResult?.message}. Using local paraphrasing fallback.`);
            console.log("Applying local paraphrasing fallback...");
            modifiedText = await simpleParaphrase(modifiedText);
            console.log("Local paraphrasing completed.");
        }

        // --- Step 2: Apply other subtle modifications from textModifiers.js ---
        // These functions from textModifiers.js are synchronous and operate on the text
        console.log("Applying controlled mistakes...");
        modifiedText = addControlledMistakes(modifiedText);

        console.log("Applying style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 3: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        res.status(200).json({ modifiedText, detectionResult });

    } catch (error) {
        // This catches errors from textModifiers or other unexpected issues within this handler
        console.error("Error in /api/process main try block:", error);
        const errorMessage = error.message || 'Error processing text.';
        res.status(500).json({
            message: errorMessage,
            error: error.toString(),
            detectionResult: { // Ensure detectionResult has a consistent error structure
                error: true,
                status: "Server Error",
                message: "Failed to process text due to an internal server error in the API handler.",
                score: null
            }
        });
    }
}
