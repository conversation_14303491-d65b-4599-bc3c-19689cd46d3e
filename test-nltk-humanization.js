/**
 * Test script for the NLTK-inspired humanization system
 * Demonstrates POS tagging, synonym replacement, and text cleaning functionality
 * Based on the Python NLTK approach but implemented in JavaScript
 */

import { humanizeText } from './src/services/humaneyesService.js';
import { humanizeWithNLTKApproach } from './src/services/falconService.js';

// Test texts with different characteristics
const testTexts = [
    // Simple text with adjectives and adverbs
    "This is a very good example of how the system works effectively. The results are quite impressive and show significant improvements.",
    
    // More complex text with various POS tags
    "The advanced algorithm demonstrates remarkable performance in processing large datasets. It efficiently handles complex operations while maintaining high accuracy levels.",
    
    // Text with formal language patterns
    "Furthermore, the implementation reveals substantial benefits in terms of computational efficiency. Moreover, the system exhibits excellent scalability characteristics.",
    
    // Technical content
    "The API provides comprehensive functionality for data processing. Users can easily integrate the service into their existing applications.",
    
    // Mixed content with line breaks
    `The system offers several key advantages:

1. High performance processing
2. Excellent accuracy rates
3. Easy integration capabilities

These features make it particularly suitable for enterprise applications.`
];

async function testNLTKHumanization() {
    console.log('🔬 Testing NLTK-inspired Humanization System');
    console.log('=' .repeat(60));
    
    for (let i = 0; i < testTexts.length; i++) {
        const originalText = testTexts[i];
        console.log(`\n📝 Test ${i + 1}:`);
        console.log('Original:', originalText);
        console.log('-'.repeat(40));
        
        try {
            // Test direct NLTK approach
            const nltkResult = await humanizeWithNLTKApproach(originalText, {
                aggressiveness: 0.7,
                maintainTone: true,
                targetDetection: 10,
                useAdvancedSynonyms: true
            });
            
            if (nltkResult.success) {
                console.log('✅ NLTK Result:', nltkResult.text);
                console.log(`📊 Stats: ${nltkResult.originalText.length} → ${nltkResult.text.length} chars (${(nltkResult.transformationRate * 100).toFixed(1)}% change)`);
                console.log(`⏱️  Processing time: ${nltkResult.processingTime}ms`);
            } else {
                console.log('❌ NLTK failed:', nltkResult.error);
            }
            
            // Test through main service with NLTK method
            console.log('\n🔄 Testing through main service...');
            const serviceResult = await humanizeText(originalText, {
                method: 'nltk',
                aggressiveness: 0.7,
                targetDetection: 10
            });
            
            if (serviceResult.success) {
                console.log('✅ Service Result:', serviceResult.text);
                console.log(`📈 Method: ${serviceResult.method}`);
            } else {
                console.log('❌ Service failed:', serviceResult.error);
            }
            
        } catch (error) {
            console.error('💥 Test failed:', error.message);
        }
        
        console.log('='.repeat(60));
    }
}

async function testAutoMethodSelection() {
    console.log('\n🎯 Testing Auto Method Selection');
    console.log('=' .repeat(60));
    
    const testText = "This is a very good example that demonstrates excellent performance and shows remarkable results.";
    
    // Test multiple runs to see method selection variety
    for (let i = 0; i < 5; i++) {
        console.log(`\n🔄 Run ${i + 1}:`);
        
        try {
            const result = await humanizeText(testText, {
                method: 'auto',
                aggressiveness: 0.7,
                targetDetection: 10
            });
            
            if (result.success) {
                console.log(`📋 Selected method: ${result.method}`);
                console.log(`📝 Result: ${result.text}`);
            } else {
                console.log('❌ Failed:', result.error);
            }
        } catch (error) {
            console.error('💥 Error:', error.message);
        }
    }
}

async function compareApproaches() {
    console.log('\n⚖️  Comparing Different Approaches');
    console.log('=' .repeat(60));
    
    const testText = "The system demonstrates very good performance with excellent results. It shows remarkable efficiency and provides outstanding capabilities.";
    
    const methods = ['nltk', 'pattern'];
    
    for (const method of methods) {
        console.log(`\n🔧 Testing ${method.toUpperCase()} method:`);
        
        try {
            const result = await humanizeText(testText, {
                method: method,
                aggressiveness: 0.7,
                targetDetection: 10
            });
            
            if (result.success) {
                console.log(`📝 Result: ${result.text}`);
                console.log(`⏱️  Time: ${result.processingTime}ms`);
                console.log(`📊 Method: ${result.method}`);
            } else {
                console.log('❌ Failed:', result.error);
            }
        } catch (error) {
            console.error('💥 Error:', error.message);
        }
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting NLTK-inspired Humanization Tests\n');
    
    try {
        await testNLTKHumanization();
        await testAutoMethodSelection();
        await compareApproaches();
        
        console.log('\n✅ All tests completed!');
    } catch (error) {
        console.error('💥 Test suite failed:', error);
    }
}

// Execute tests
runAllTests().catch(console.error);
