import React, { useState } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import Header from '../components/layout/Header';
import TextEditor from '../components/features/TextEditor';
import ProcessingButton from '../components/features/ProcessingButton';
import ResultsDisplay from '../components/features/ResultsDisplay';
import { processTextApi } from '../utils/apiClient';
import styles from '../styles/Home.module.css';

export default function HomePage() {
    const { data: session, status: sessionStatus } = useSession();
    const isSessionLoading = sessionStatus === 'loading';

    const [inputText, setInputText] = useState('');
    const [outputText, setOutputText] = useState('');
    const [detectionResult, setDetectionResult] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [processingError, setProcessingError] = useState('');
    const [processingStats, setProcessingStats] = useState(null);

    const handleInputChange = (e) => {
        setInputText(e.target.value);
        if (processingError) setProcessingError('');
    };

    const handleProcessText = async () => {
        if (!inputText.trim()) {
            setProcessingError('Please enter some text to process.');
            return;
        }

        setIsProcessing(true);
        setProcessingError('');
        setOutputText('');
        setDetectionResult(null);
        setProcessingStats(null);

        const startTime = Date.now();

        try {
            const response = await processTextApi({ text: inputText });
            const processingTime = Date.now() - startTime;

            setOutputText(response.modifiedText);
            setDetectionResult(response.detectionResult);
            setProcessingStats({
                processingTime,
                originalLength: inputText.length,
                modifiedLength: response.modifiedText.length,
                wordsChanged: calculateWordsChanged(inputText, response.modifiedText)
            });
        } catch (err) {
            setProcessingError(err.message || 'Failed to process text. Please try again.');
            console.error('Processing error:', err);
        } finally {
            setIsProcessing(false);
        }
    };

    const calculateWordsChanged = (original, modified) => {
        const originalWords = original.trim().split(/\s+/);
        const modifiedWords = modified.trim().split(/\s+/);
        let changes = 0;

        for (let i = 0; i < Math.min(originalWords.length, modifiedWords.length); i++) {
            if (originalWords[i] !== modifiedWords[i]) {
                changes++;
            }
        }

        return changes + Math.abs(originalWords.length - modifiedWords.length);
    };

    const handleCopy = (text, type) => {
        console.log(`Copied ${type} text:`, text.substring(0, 50) + '...');
    };

    const handleDownload = (text) => {
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'humanized-text.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <>
            <Head>
                <title>GhostLayer - Transform AI Text to Human-Like Content</title>
                <meta name="description" content="Transform AI-generated text into human-like content that bypasses AI detection. Free online tool with advanced paraphrasing and text modification." />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <meta property="og:title" content="GhostLayer - Transform AI Text to Human-Like Content" />
                <meta property="og:description" content="Transform AI-generated text into human-like content that bypasses AI detection." />
                <meta property="og:type" content="website" />
                <link rel="icon" href="/favicon.ico" />
            </Head>

            <div className={styles.app}>
                <Header />

                <main className={styles.main}>
                    {/* Hero Section */}
                    <section className={styles.hero}>
                        <div className={styles.heroContent}>
                            <h1 className={styles.heroTitle}>
                                Transform AI Text to
                                <span className={styles.highlight}> Human-Like Content</span>
                            </h1>
                            <p className={styles.heroSubtitle}>
                                Make your AI-generated text undetectable with our advanced paraphrasing and
                                text modification technology. Free to use, instant results.
                            </p>
                            <div className={styles.heroStats}>
                                <div className={styles.stat}>
                                    <span className={styles.statNumber}>10K+</span>
                                    <span className={styles.statLabel}>Texts Processed</span>
                                </div>
                                <div className={styles.stat}>
                                    <span className={styles.statNumber}>95%</span>
                                    <span className={styles.statLabel}>Success Rate</span>
                                </div>
                                <div className={styles.stat}>
                                    <span className={styles.statNumber}>&lt; 5s</span>
                                    <span className={styles.statLabel}>Processing Time</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Text Processing Section */}
                    <section className={styles.processingSection}>
                        <TextEditor
                            inputText={inputText}
                            onInputChange={handleInputChange}
                            outputText={outputText}
                            isLoading={isProcessing}
                            onCopy={handleCopy}
                            onDownload={handleDownload}
                        />

                        <ProcessingButton
                            onProcess={handleProcessText}
                            isLoading={isProcessing}
                            disabled={!inputText.trim()}
                            inputText={inputText}
                        />

                        {processingError && (
                            <div className={styles.errorMessage}>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                                    <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                                    <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                <span>{processingError}</span>
                            </div>
                        )}

                        {processingStats && (
                            <div className={styles.statsDisplay}>
                                <h3>Processing Results</h3>
                                <div className={styles.statsGrid}>
                                    <div className={styles.statItem}>
                                        <span className={styles.statValue}>{processingStats.processingTime}ms</span>
                                        <span className={styles.statLabel}>Processing Time</span>
                                    </div>
                                    <div className={styles.statItem}>
                                        <span className={styles.statValue}>{processingStats.wordsChanged}</span>
                                        <span className={styles.statLabel}>Words Modified</span>
                                    </div>
                                    <div className={styles.statItem}>
                                        <span className={styles.statValue}>
                                            {Math.round((processingStats.wordsChanged / inputText.trim().split(/\s+/).length) * 100)}%
                                        </span>
                                        <span className={styles.statLabel}>Change Rate</span>
                                    </div>
                                </div>
                            </div>
                        )}

                        {detectionResult && (
                            <ResultsDisplay
                                outputText={outputText}
                                detectionResult={detectionResult}
                                isLoading={isProcessing}
                            />
                        )}
                    </section>
                </main>
            </div>
        </>
    );
}
