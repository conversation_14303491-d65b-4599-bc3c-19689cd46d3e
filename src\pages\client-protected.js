import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router'; // Import useRouter
import Layout from '../components/layout/Layout';
import React, { useState, useEffect } from 'react';
import styles from '../styles/Home.module.css'; // Re-use some Home styles for messages
import pricingStyles from '../styles/PricingPage.module.css'; // Re-use button styles from PricingPage

export default function ClientProtectedPage() {
    const { data: session, status: sessionStatus } = useSession({
        required: true,
        onUnauthenticated() {
            console.log("User is not authenticated on ClientProtectedPage. NextAuth will handle redirect.");
        }
    });
    const router = useRouter(); // Initialize router

    const isSessionLoading = sessionStatus === 'loading';

    const [userProfile, setUserProfile] = useState(null);
    const [profileError, setProfileError] = useState(null);
    const [isProfileFetching, setIsProfileFetching] = useState(false);

    const [premiumFeatureMessage, setPremiumFeatureMessage] = useState('');

    // State for managing subscription portal redirection
    const [isManagingSubscription, setIsManagingSubscription] = useState(false);
    const [manageSubscriptionError, setManageSubscriptionError] = useState('');


    useEffect(() => {
        if (session?.user?.id && !userProfile && !isProfileFetching) {
            setIsProfileFetching(true);
            setProfileError(null);

            fetch('/api/user-profile')
                .then(async (res) => {
                    if (!res.ok) {
                        let errorData;
                        try {
                            errorData = await res.json();
                        } catch (e) {
                            errorData = { message: `Error ${res.status}: Failed to fetch profile details.` };
                        }
                        throw new Error(errorData.message || errorData.error || `Error ${res.status}`);
                    }
                    return res.json();
                })
                .then(data => {
                    setUserProfile(data);
                })
                .catch(err => {
                    console.error("Failed to fetch user profile:", err);
                    setProfileError(err.message);
                    setUserProfile(null);
                })
                .finally(() => {
                    setIsProfileFetching(false);
                });
        }
    }, [session, userProfile, isProfileFetching]);

    const handleAccessPremiumFeature = async () => {
        setPremiumFeatureMessage('Attempting to access premium feature...');
        try {
            const response = await fetch('/api/premium-feature');
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.message || data.error || `Error ${response.status}`);
            }
            setPremiumFeatureMessage(`Premium Feature Response: ${data.message} - Data: ${data.data}`);
        } catch (error) {
            console.error("Premium feature access error:", error);
            setPremiumFeatureMessage(`Error: ${error.message}`);
        }
    };

    const handleManageSubscription = async () => {
        setIsManagingSubscription(true);
        setManageSubscriptionError('');
        try {
            const res = await fetch('/api/stripe/create-customer-portal-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });

            if (!res.ok) {
                const errorData = await res.json().catch(() => ({ message: 'Failed to create customer portal session. Please try again.' }));
                throw new Error(errorData.message || errorData.error || `Error: ${res.status}`);
            }

            const { url } = await res.json();
            if (url) {
                window.location.href = url; // Redirect to Stripe Customer Portal
            } else {
                throw new Error('Customer portal URL not received from server.');
            }
        } catch (error) {
            console.error('Manage subscription error:', error.message);
            setManageSubscriptionError(`Could not open subscription management: ${error.message}`);
        } finally {
            setIsManagingSubscription(false);
        }
    };


    if (isSessionLoading || (session && isProfileFetching && !userProfile && !profileError)) {
        return (
            <Layout>
                <div className={styles.container}>
                    <p className={styles.loadingMessage}>Loading session or profile...</p>
                </div>
            </Layout>
        );
    }

    if (!session) {
        return (
            <Layout>
                <div className={styles.container}>
                    <p className={styles.signInPrompt}>You are not signed in. Redirecting...</p>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className={styles.container}>
                <h1 className={styles.title}>User Dashboard</h1> {/* Changed title */}

                <div className={styles.protectedContent} style={{ marginBottom: '20px', textAlign: 'left' }}>
                    <h3>Welcome, {session.user.name || session.user.email}!</h3>
                    <p>This is your protected area. Your profile information from our database is shown below.</p>
                </div>

                {profileError && (
                    <div className={styles.error} style={{ marginBottom: '20px' }}>
                        <p>Error loading your full profile: {profileError}</p>
                        <p>Some features might be unavailable. Please try refreshing the page.</p>
                    </div>
                )}

                {userProfile ? (
                    <div className={pricingStyles.planCard} style={{ marginBottom: '20px', textAlign: 'left', border: `2px solid ${userProfile.subscriptionTier === 'premium' ? '#28a745' : '#0070f3'}` }}> {/* Using planCard style */}
                        <h4 className={pricingStyles.planTitle} style={{fontSize: '1.5rem'}}>Your Profile Details:</h4>
                        <ul className={pricingStyles.featuresList} style={{fontSize: '1rem'}}>
                            <li><strong>ID:</strong> {userProfile.id}</li>
                            <li><strong>Email:</strong> {userProfile.email}</li>
                            <li><strong>Name:</strong> {userProfile.name || 'N/A'}</li>
                            <li style={{ color: userProfile.subscriptionTier === 'premium' ? '#28a745' : 'inherit' }}>
                                <strong>Subscription Tier:</strong> <span style={{ fontWeight: 'bold' }}>{userProfile.subscriptionTier}</span>
                            </li>
                            <li><strong>Usage Credits:</strong> {userProfile.usageCredits}</li>
                            <li><strong>Member Since:</strong> {new Date(userProfile.createdAt).toLocaleDateString()}</li>
                        </ul>
                        {userProfile.subscriptionTier === 'premium' && (
                             <button
                                onClick={handleManageSubscription}
                                disabled={isManagingSubscription}
                                className={pricingStyles.planButton} // Re-use pricing page button style
                                style={{marginTop: '15px'}}
                            >
                                {isManagingSubscription ? 'Processing...' : 'Manage Subscription'}
                            </button>
                        )}
                        {manageSubscriptionError && <p style={{color: 'red', marginTop: '10px', fontSize: '0.9rem'}}>{manageSubscriptionError}</p>}
                    </div>
                ) : (
                    !profileError && <p className={styles.loadingMessage}>Loading your extended profile details...</p>
                )}

                {/* Feature Gating UI based on subscriptionTier from userProfile */}
                {userProfile && userProfile.subscriptionTier === 'premium' && (
                    <div className={styles.protectedContent} style={{borderColor: 'green', backgroundColor: '#e6ffe6', marginBottom: '20px'}}>
                        <h4>Premium Features Access</h4>
                        <p>As a premium user, you can access exclusive API endpoints.</p>
                        <button onClick={handleAccessPremiumFeature} className={pricingStyles.planButton} style={{backgroundColor: '#5cb85c', borderColor: '#4cae4c'}}>
                            Test Premium API Endpoint
                        </button>
                    </div>
                )}

                {userProfile && userProfile.subscriptionTier !== 'premium' && (
                    <div className={styles.signInPrompt} style={{borderColor: 'orange', backgroundColor: '#fff8e1', marginBottom: '20px'}}>
                        <h4>Upgrade to Premium</h4>
                        <p>Unlock more features, higher limits, and an ad-free experience by upgrading to Premium.</p>
                        <button onClick={() => router.push('/pricing')} className={pricingStyles.planButton} style={{backgroundColor: 'orange'}}>
                            View Pricing / Upgrade
                        </button>
                        <br/>
                        <button onClick={handleAccessPremiumFeature} className={pricingStyles.planButton} style={{backgroundColor: 'grey', marginTop: '10px'}}>
                            (Test Premium API - Expect Denied)
                        </button>
                    </div>
                )}
                {premiumFeatureMessage && <p className={styles.loadingMessage} style={{fontSize: '0.9rem', padding: '10px', backgroundColor: '#f9f9f9', border: '1px solid #eee', marginTop: '10px'}}>{premiumFeatureMessage}</p>}

                <div style={{ marginTop: '30px', textAlign: 'left', backgroundColor: '#f9f9f9', padding: '15px', borderRadius: '8px' }}>
                    <h4>Raw Session Object (Client-Side):</h4>
                    <pre style={{ backgroundColor: '#f0f0f0', padding: '15px', borderRadius: '5px', overflowX: 'auto', fontSize: '0.85rem' }}>
                        {JSON.stringify(session, null, 2)}
                    </pre>
                </div>
            </div>
        </Layout>
    );
}
