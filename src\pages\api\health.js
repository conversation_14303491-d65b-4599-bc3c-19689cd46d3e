import { PrismaClient } from '@prisma/client';
import { async<PERSON>and<PERSON> } from '../../lib/errorHandler';
import logger from '../../lib/logger';

const prisma = new PrismaClient();

// Health check endpoint for monitoring and load balancers
const healthCheck = async (req, res) => {
  if (req.method !== 'GET') {
    res.setHeader('Allow', 'GET');
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const startTime = Date.now();
  const checks = {
    database: false,
    environment: false,
    services: false,
  };
  
  let overallStatus = 'healthy';
  const details = {};

  try {
    // Check database connectivity
    try {
      await prisma.$queryRaw`SELECT 1`;
      checks.database = true;
      details.database = 'Connected';
    } catch (error) {
      checks.database = false;
      details.database = `Error: ${error.message}`;
      overallStatus = 'unhealthy';
      logger.error('Health check: Database connection failed', error);
    }

    // Check environment variables
    const requiredEnvVars = [
      'NEXTAUTH_SECRET',
      'DATABASE_URL',
    ];
    
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length === 0) {
      checks.environment = true;
      details.environment = 'All required environment variables are set';
    } else {
      checks.environment = false;
      details.environment = `Missing: ${missingEnvVars.join(', ')}`;
      overallStatus = 'degraded';
    }

    // Check external services (optional)
    const serviceChecks = {};
    
    // Check if Stripe is configured
    if (process.env.STRIPE_SECRET_KEY) {
      serviceChecks.stripe = 'Configured';
    } else {
      serviceChecks.stripe = 'Not configured';
    }
    
    // Check if GPTZero is configured
    if (process.env.GPTZERO_API_KEY) {
      serviceChecks.gptzero = 'Configured';
    } else {
      serviceChecks.gptzero = 'Not configured';
    }
    
    // Check if paraphrase service is configured
    if (process.env.NEXT_PUBLIC_PARAPHRASE_API_URL) {
      serviceChecks.paraphraseService = 'Configured';
    } else {
      serviceChecks.paraphraseService = 'Not configured';
    }
    
    checks.services = true;
    details.services = serviceChecks;

  } catch (error) {
    overallStatus = 'unhealthy';
    logger.error('Health check failed', error);
  }

  const responseTime = Date.now() - startTime;
  
  // Determine HTTP status code
  let statusCode = 200;
  if (overallStatus === 'unhealthy') {
    statusCode = 503; // Service Unavailable
  } else if (overallStatus === 'degraded') {
    statusCode = 200; // OK but with warnings
  }

  const response = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    responseTime: `${responseTime}ms`,
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks,
    details,
    system: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
    },
  };

  // Log health check if there are issues
  if (overallStatus !== 'healthy') {
    logger.warn('Health check completed with issues', {
      status: overallStatus,
      checks,
      responseTime,
    });
  }

  res.status(statusCode).json(response);
};

export default asyncHandler(healthCheck);
