/**
 * Test script for the DeepSeek-R1 with DeepThink reasoning humanization system
 * Verifies that the enhanced LLM-based humanization with reasoning capabilities is working correctly
 * Tests ≤10% AI detection achievement with DeepSeek-R1 as primary model
 */

import { humanizeText } from './src/services/humaneyesService.js';
import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable, getProviderStatus } from './src/services/falconService.js';
import { analyzeAIPatterns } from './src/utils/localAIDetector.js';

// Test content with varying AI detection risk levels
const testCases = [
    {
        name: "High AI Content (DeepThink Test)",
        text: `The implementation of artificial intelligence systems requires careful consideration of multiple factors. Furthermore, it is essential to ensure that the deployment process follows established protocols. Moreover, the optimization of performance metrics must be thoroughly evaluated. Additionally, comprehensive testing procedures should be implemented to validate system functionality.`,
        expectedImprovement: "DeepSeek-R1 should reduce from ~60% to ≤10% AI detection using reasoning"
    },
    {
        name: "Moderate AI Content (Reasoning Chain Test)",
        text: `This document outlines the key strategies for improving business operations. The analysis reveals several important findings that organizations should consider. Implementation of these recommendations will result in enhanced efficiency and improved outcomes.`,
        expectedImprovement: "DeepSeek-R1 should reduce from ~30% to ≤10% with natural reasoning flow"
    },
    {
        name: "Technical Content (DeepThink Precision Test)",
        text: `The API endpoint configuration requires proper authentication headers and CORS settings. Database connections should utilize connection pooling for optimal performance. Error handling mechanisms must be implemented to ensure system reliability.`,
        expectedImprovement: "DeepSeek-R1 should maintain technical accuracy while achieving ≤10% detection"
    },
    {
        name: "Complex Reasoning Test",
        text: `The systematic approach to problem-solving involves multiple sequential steps. First, comprehensive analysis of the situation must be conducted. Subsequently, various potential solutions should be evaluated based on predetermined criteria. Finally, the optimal solution must be selected and implemented according to established best practices.`,
        expectedImprovement: "DeepSeek-R1 DeepThink should excel at humanizing complex logical structures"
    }
];

/**
 * Test the DeepSeek-R1 with DeepThink reasoning humanization system
 */
async function testAdvancedHumanization() {
    console.log('🧠 TESTING DEEPSEEK-R1 WITH DEEPTHINK REASONING HUMANIZATION');
    console.log('═'.repeat(70));
    
    // Check provider availability
    console.log('\n📊 PROVIDER STATUS CHECK:');
    console.log('─'.repeat(40));
    
    const isAvailable = isAdvancedLLMAvailable();
    console.log(`Advanced LLM Available: ${isAvailable ? '✅ YES' : '❌ NO'}`);
    
    if (isAvailable) {
        const providerStatus = getProviderStatus();
        Object.entries(providerStatus).forEach(([model, providers]) => {
            console.log(`\n${model}:`);
            providers.forEach(provider => {
                console.log(`  ${provider.name}: ${provider.available ? '✅' : '❌'} (${provider.model})`);
            });
        });
    } else {
        console.log('\n⚠️  No API keys configured. Please set up at least one of:');
        console.log('   - FIREWORKS_API_KEY (🥇 Primary for DeepSeek-R1)');
        console.log('   - NOVITA_API_KEY (DeepSeek-R1 alternative)');
        console.log('   - OPENROUTER_API_KEY (DeepSeek-R1 access)');
        console.log('   - GROQ_API_KEY (fallback only)');
        console.log('\n   Testing will use pattern-based fallback only.');
        console.log('   🧠 DeepThink reasoning capabilities require API access.');
    }
    
    // Test each case
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n\n🧪 TEST CASE ${i + 1}: ${testCase.name}`);
        console.log('═'.repeat(50));
        
        console.log('\n📄 ORIGINAL TEXT:');
        console.log('─'.repeat(30));
        console.log(testCase.text);
        
        // Analyze original AI patterns
        console.log('\n🔍 ORIGINAL AI ANALYSIS:');
        console.log('─'.repeat(30));
        const originalAnalysis = analyzeAIPatterns(testCase.text);
        console.log(`AI Detection Score: ${originalAnalysis.score}%`);
        console.log(`Confidence: ${(originalAnalysis.confidence * 100).toFixed(0)}%`);
        
        if (Object.keys(originalAnalysis.patterns).length > 0) {
            console.log('Detected Patterns:');
            Object.entries(originalAnalysis.patterns).forEach(([pattern, data]) => {
                console.log(`  - ${pattern}: ${data.matches} matches (score: ${data.score.toFixed(1)})`);
            });
        }
        
        // Test DeepSeek-R1 humanization
        console.log('\n🧠 DEEPSEEK-R1 DEEPTHINK PROCESSING:');
        console.log('─'.repeat(30));

        const startTime = Date.now();

        try {
            const result = await humanizeText(testCase.text, {
                aggressiveness: 0.8, // High aggressiveness for better results
                maintainTone: true,
                targetDetection: 10,
                method: 'llm', // Force LLM-based (DeepSeek-R1 priority)
                fallbackEnabled: true
            });
            
            const processingTime = Date.now() - startTime;
            
            if (result.success) {
                console.log(`✅ SUCCESS (${processingTime}ms)`);
                console.log(`Method: ${result.actualMethod || result.method}`);
                if (result.model) {
                    console.log(`Model: ${result.model}${result.provider ? ` (${result.provider})` : ''}`);
                }
                
                console.log('\n📝 HUMANIZED TEXT:');
                console.log('─'.repeat(30));
                console.log(result.humanizedText);
                
                // Analyze humanized text
                console.log('\n🎯 HUMANIZED AI ANALYSIS:');
                console.log('─'.repeat(30));
                const humanizedAnalysis = analyzeAIPatterns(result.humanizedText);
                console.log(`AI Detection Score: ${humanizedAnalysis.score}%`);
                console.log(`Confidence: ${(humanizedAnalysis.confidence * 100).toFixed(0)}%`);
                
                // Calculate improvement
                const improvement = originalAnalysis.score - humanizedAnalysis.score;
                const improvementPercent = ((improvement / originalAnalysis.score) * 100).toFixed(1);
                
                console.log('\n📊 IMPROVEMENT METRICS:');
                console.log('─'.repeat(30));
                console.log(`Score Reduction: ${improvement.toFixed(1)}% (${improvementPercent}% improvement)`);
                console.log(`Target Achievement: ${humanizedAnalysis.score <= 10 ? '✅ ACHIEVED' : '❌ NEEDS IMPROVEMENT'} (≤10% target)`);
                console.log(`Expected: ${testCase.expectedImprovement}`);
                
                // Quality metrics
                const originalWords = testCase.text.split(/\s+/).length;
                const humanizedWords = result.humanizedText.split(/\s+/).length;
                const lengthChange = ((humanizedWords - originalWords) / originalWords * 100).toFixed(1);
                
                console.log('\n📏 QUALITY METRICS:');
                console.log('─'.repeat(30));
                console.log(`Length Change: ${lengthChange}% (${originalWords} → ${humanizedWords} words)`);
                console.log(`Processing Time: ${processingTime}ms`);
                
            } else {
                console.log(`❌ FAILED: ${result.error}`);
                console.log(`Method Attempted: ${result.requestedMethod || 'unknown'}`);
                
                if (result.fallbackRecommended) {
                    console.log('💡 Recommendation: Check API keys and try again');
                }
            }
            
        } catch (error) {
            console.log(`❌ ERROR: ${error.message}`);
        }
    }
    
    console.log('\n\n🎉 TESTING COMPLETED');
    console.log('═'.repeat(70));
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('─'.repeat(30));
    
    if (!isAdvancedLLMAvailable()) {
        console.log('1. Set up Fireworks AI API key for best results');
        console.log('2. Consider Groq API for free fast processing');
        console.log('3. Add multiple providers for redundancy');
    } else {
        console.log('1. Monitor API usage and costs');
        console.log('2. Test with your specific content types');
        console.log('3. Adjust aggressiveness based on results');
    }
    
    console.log('\n🔗 Setup Guide: See ENVIRONMENT_VARIABLES_GUIDE.md');
}

/**
 * Test direct DeepSeek-R1 with DeepThink reasoning
 */
async function testDirectAdvancedLLM() {
    console.log('\n\n🧠 DIRECT DEEPSEEK-R1 DEEPTHINK TEST');
    console.log('═'.repeat(50));

    if (!isAdvancedLLMAvailable()) {
        console.log('❌ No DeepSeek-R1 API keys configured');
        console.log('   Please set up FIREWORKS_API_KEY, NOVITA_API_KEY, or OPENROUTER_API_KEY');
        return;
    }

    const testTexts = [
        {
            name: "Complex Reasoning Test",
            text: "The implementation of this solution requires comprehensive analysis and systematic evaluation of all relevant factors."
        },
        {
            name: "DeepThink Logic Test",
            text: "Furthermore, it is important to note that the systematic approach involves multiple sequential steps that must be carefully coordinated."
        }
    ];

    for (const testCase of testTexts) {
        console.log(`\n🔬 Testing ${testCase.name}...`);
        console.log(`Original: ${testCase.text}`);

        try {
            const result = await humanizeWithAdvancedLLM(testCase.text, {
                aggressiveness: 0.9, // High for DeepThink testing
                maintainTone: true,
                targetDetection: 10,
                preferredModel: 'deepseek-r1',
                enableDeepThink: true // Explicitly enable DeepThink
            });

            if (result.success) {
                console.log(`✅ DeepSeek-R1 Success: ${result.provider}/${result.modelName}`);
                console.log(`⏱️  Processing Time: ${result.processingTime}ms`);
                console.log(`🧠 Multi-pass: ${result.multiPass ? 'Yes' : 'No'}`);
                console.log(`🎯 Detection Validation: ${result.detectionValidation ? 'Yes' : 'No'}`);
                console.log(`📝 Humanized: ${result.text}`);

                // Check DeepThink reasoning validation
                if (result.reasoningValidation) {
                    console.log(`🧠 DeepThink Reasoning: ${result.reasoningValidation.hasReasoning ? 'Found' : 'Missing'}`);
                    console.log(`🔍 Reasoning Quality: ${(result.reasoningValidation.reasoningQuality * 100).toFixed(1)}%`);
                    console.log(`📊 Reasoning Steps: ${result.reasoningValidation.reasoningSteps}`);
                }

                if (result.detectionValidation) {
                    console.log(`🔍 AI Detection: ${result.detectionValidation.score?.toFixed(1)}%`);
                    console.log(`✅ Meets ≤10% target: ${result.detectionValidation.meetsTarget ? 'Yes' : 'No'}`);
                }
            } else {
                console.log(`❌ DeepSeek-R1 Failed: ${result.error}`);
            }

        } catch (error) {
            console.log(`❌ DeepSeek-R1 Error: ${error.message}`);
        }
    }
}

// Run the tests
async function runAllTests() {
    try {
        await testAdvancedHumanization();
        await testDirectAdvancedLLM();
    } catch (error) {
        console.error('Test execution failed:', error);
    }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}

export { testAdvancedHumanization, testDirectAdvancedLLM };
