import { getServerSession } from "next-auth/next"
import { authOptions } from "./auth/[...nextauth]" // Assumes authOptions is exported from your main next-auth config

export default async function handler(req, res) {
    // Get the session from the request using getServerSession
    // This is the recommended way to access the session on the server-side (API Routes, getServerSideProps)
    const session = await getServerSession(req, res, authOptions);

    if (session) {
        // If session exists, the user is authenticated.
        // You can access user data via session.user
        res.status(200).json({
            message: "This is protected data accessible only to authenticated users.",
            user: session.user,
            // You could also include sensitive data that only authenticated users should see
            // For example: userSpecificData: "Details for " + session.user.email
        });
    } else {
        // If no session exists, the user is not authenticated.
        // Return an error or a specific status code.
        res.status(401).json({
            error: "Unauthorized",
            message: "You must be signed in to access this resource.",
        });
    }
}
