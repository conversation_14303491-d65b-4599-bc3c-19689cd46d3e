// Simple debug script for NLTK sentence splitting
const text = "The system works effectively. It shows good results.";
const newlinePlaceholder = "庄周";
const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

console.log('Original:', text);
console.log('With placeholders:', textWithPlaceholders);

// Test different regex patterns
const patterns = [
    /[^.!?]+[.!?]+|[^\w\s]+/g,  // Original
    /[^.!?]*[.!?]+\s*|[^.!?]+$/g,  // New attempt
    /[^.!?]*[.!?]+/g,  // Simpler
    /[^.!?]+[.!?]+/g   // Even simpler
];

patterns.forEach((pattern, i) => {
    console.log(`\nPattern ${i + 1}:`, pattern);
    const sentences = textWithPlaceholders.match(pattern) || [textWithPlaceholders];
    console.log('Sentences:', sentences);
});

// Test the tokenization
const sentence = "The system works effectively.";
const tokens = sentence.match(/\w+|[^\w\s]+/g) || [];
console.log('\nTokens for "' + sentence + '":', tokens);

// Test reconstruction
let reconstructed = '';
for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];
    
    // Add space before word tokens (except first token)
    if (i > 0 && /^\w/.test(token) && /\w$/.test(tokens[i-1])) {
        reconstructed += ' ';
    }
    
    reconstructed += token;
}

console.log('Reconstructed:', reconstructed);
