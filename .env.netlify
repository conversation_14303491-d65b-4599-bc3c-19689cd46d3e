# Netlify-specific environment variables
# These are minimal required variables for static build

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://ghostlayer-ai.netlify.app
NEXT_PUBLIC_APP_NAME="GhostLayer"

# NextAuth.js Configuration (dummy values for build)
NEXTAUTH_SECRET=netlify_build_secret_placeholder
NEXTAUTH_URL=https://ghostlayer-ai.netlify.app

# Database Configuration (placeholder for static build)
DATABASE_URL="placeholder://static-build-no-db-needed"

# Disable features that require API keys during build
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_AI_DETECTION=false
NEXT_PUBLIC_ENABLE_PREMIUM_FEATURES=false

# Build-specific flags
NETLIFY=true
NEXT_TELEMETRY_DISABLED=1
HUSKY=0
