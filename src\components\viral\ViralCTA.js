import React, { useState, useEffect } from 'react';
import SocialShare from './SocialShare';
import styles from '../../styles/ViralCTA.module.css';

const ViralCTA = ({ 
  variant = "default", // "default", "success", "floating"
  showSocialShare = true,
  customMessage = null,
  onCTAClick = null
}) => {
  const [isVisible, setIsVisible] = useState(variant !== "floating");
  const [userCount, setUserCount] = useState(null); // Start with null to prevent hydration mismatch
  const [isClient, setIsClient] = useState(false);
  const [currentUsers, setCurrentUsers] = useState(8); // Static initial value for SSR

  useEffect(() => {
    setIsClient(true);
    // Initialize dynamic values only on client side
    setUserCount(Math.floor(Math.random() * 50000) + 100000); // Random between 100k-150k
    setCurrentUsers(Math.floor(Math.random() * 15) + 5); // Random between 5-20

    if (variant === "floating") {
      const timer = setTimeout(() => setIsVisible(true), 3000);
      return () => clearTimeout(timer);
    }
  }, [variant]);

  useEffect(() => {
    // Simulate real-time user count updates (only on client)
    if (!isClient || !userCount) return;

    const interval = setInterval(() => {
      setUserCount(prev => prev + Math.floor(Math.random() * 100) + 50); // Increase by 50-150
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [isClient, userCount]);

  useEffect(() => {
    // Update current users count periodically (only on client)
    if (!isClient) return;

    const interval = setInterval(() => {
      setCurrentUsers(Math.floor(Math.random() * 15) + 5); // Random between 5-20
    }, 45000); // Update every 45 seconds

    return () => clearInterval(interval);
  }, [isClient]);

  // Format user count as "10k+" or "100k+"
  const formatUserCount = (count) => {
    if (!count) return "100k+"; // Default fallback for SSR
    if (count >= 1000000) {
      return Math.floor(count / 1000000) + "M+";
    } else if (count >= 1000) {
      return Math.floor(count / 1000) + "k+";
    }
    return count + "+";
  };

  // Get display value - static on server, dynamic on client
  const getDisplayUserCount = () => {
    if (!isClient) return "100k+"; // Always return static value on server
    return formatUserCount(userCount);
  };

  // Get current users display - static on server, dynamic on client
  const getDisplayCurrentUsers = () => {
    if (!isClient) return 8; // Always return static value on server
    return currentUsers;
  };

  const handleCTAClick = () => {
    if (onCTAClick) {
      onCTAClick();
    } else {
      // Scroll to top or main CTA (only on client)
      if (typeof window !== 'undefined') {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }
  };

  const getVariantContent = () => {
    switch (variant) {
      case "success":
        return {
          emoji: "🎉",
          title: "Congratulations! Your Text is Now Undetectable",
          subtitle: "Join thousands who've successfully bypassed AI detection with GhostLayer",
          buttonText: "Humanize Another Text",
          shareMessage: "I just made my AI text completely undetectable with GhostLayer! 🔥 Try it free!"
        };
      case "floating":
        return {
          emoji: "🚀",
          title: "95% Bypass Rate", // Short, impactful title
          subtitle: "Join the AI humanization revolution",
          buttonText: "Try Free",
          shareMessage: "Discovered the best AI text humanizer - GhostLayer bypasses detection 95% of the time!"
        };
      default:
        return {
          emoji: "👻",
          title: "Ready to Make Your AI Text Undetectable?",
          subtitle: "Join 10,000+ content creators who trust GhostLayer",
          buttonText: "Start Humanizing Now",
          shareMessage: "Found the perfect AI text humanizer! GhostLayer makes AI content undetectable instantly."
        };
    }
  };

  const content = getVariantContent();
  const message = customMessage || content.shareMessage;

  if (variant === "floating" && !isVisible) {
    return null;
  }

  return (
    <div className={`${styles.viralCTA} ${styles[variant]} ${isVisible ? styles.visible : ''}`}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.header}>
            <div className={styles.emoji}>{content.emoji}</div>
            <h2 className={styles.title}>{content.title}</h2>
            <p className={styles.subtitle}>{content.subtitle}</p>
          </div>

          <div className={styles.stats}>
            <div className={styles.stat}>
              <div className={styles.statNumber}>95%</div>
              <div className={styles.statLabel}>Success Rate</div>
            </div>
            {/* <div className={styles.stat}>
              <div className={styles.statNumber}>{getDisplayUserCount()}</div>
              <div className={styles.statLabel}>Happy Users</div>
            </div> */}
            <div className={styles.stat}>
              <div className={styles.statNumber}>0.8s</div>
              <div className={styles.statLabel}>Avg. Speed</div>
            </div>
          </div>

          <div className={styles.actions}>
            <button 
              className={styles.primaryButton}
              onClick={handleCTAClick}
            >
              {content.buttonText}
              <span className={styles.buttonIcon}>→</span>
            </button>
            
            {variant !== "floating" && (
              <div className={styles.socialProof}>
                <div className={styles.avatars}>
                  <div className={styles.avatar}>👨‍💼</div>
                  <div className={styles.avatar}>👩‍🎓</div>
                  <div className={styles.avatar}>👨‍💻</div>
                  <div className={styles.avatar}>👩‍🏫</div>
                  <div className={styles.avatar}>👨‍🎨</div>
                </div>
                <p className={styles.socialText}>
                  <strong>2,847 people</strong> used GhostLayer in the last 24 hours
                </p>
              </div>
            )}
          </div>

          {showSocialShare && variant !== "floating" && (
            <div className={styles.shareSection}>
              <SocialShare 
                title={message}
                description="Transform AI-generated text into human-like content that bypasses detection. Try it free!"
                hashtags={["AITextHumanizer", "BypassAIDetection", "GhostLayer", "AIWriting"]}
              />
            </div>
          )}
        </div>

        {variant === "floating" && (
          <button 
            className={styles.closeButton}
            onClick={() => setIsVisible(false)}
            title="Close"
          >
            ×
          </button>
        )}
      </div>

      {/* Viral Growth Features */}
      <div className={styles.viralFeatures}>
        <div className={styles.urgency}>
          <span className={styles.urgencyDot}></span>
          <span className={styles.urgencyText}>
            {getDisplayCurrentUsers()} people are using GhostLayer right now
          </span>
        </div>
        
        {variant === "default" && (
          <div className={styles.testimonialQuote}>
            <blockquote>
              "GhostLayer saved my content strategy. 95% bypass rate is incredible!"
            </blockquote>
            <cite>- Sarah M., Content Creator</cite>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViralCTA;
