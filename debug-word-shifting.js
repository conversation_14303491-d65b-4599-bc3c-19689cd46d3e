// Debug the word shifting issue
import { advancedHumanization } from './src/utils/advancedHumanizer.js';

const testText = `Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for future applications. Furthermore, it is important to note that machine learning algorithms can effectively analyze vast amounts of data to identify patterns and make predictions.`;

console.log('🔍 Debugging Word Shifting Issue\n');
console.log('Original:', testText);

async function debugWordShifting() {
    try {
        const result = await advancedHumanization(testText, {
            aggressiveness: 0.8,
            maintainTone: true,
            targetDetection: 10,
            useModelBased: false,
            fallbackToPatterns: true
        });

        console.log('Result:', result.text || result);

        // Check word by word
        const originalWords = testText.split(' ');
        const resultText = result.text || result;
        const resultWords = resultText.split(' ');

        console.log('\n📊 Word-by-word comparison:');
        for (let i = 0; i < Math.max(originalWords.length, resultWords.length); i++) {
            const orig = originalWords[i] || '[missing]';
            const res = resultWords[i] || '[missing]';
            const status = orig === res ? '✅' : '❌';
            console.log(`${i + 1}. ${status} "${orig}" → "${res}"`);
        }

    } catch (error) {
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
    }
}

debugWordShifting();
