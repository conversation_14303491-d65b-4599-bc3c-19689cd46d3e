/**
 * DeepSeek-R1 DeepThink Validation Test Suite
 * Comprehensive testing to verify DeepThink reasoning is working and producing better results
 */

import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable, getProviderStatus } from './src/services/falconService.js';
import { humanizeText } from './src/services/humaneyesService.js';
import { analyzeAIPatterns } from './src/utils/localAIDetector.js';

// Test cases specifically designed to validate DeepThink reasoning
const deepThinkTestCases = [
    {
        name: "High AI Pattern Density",
        text: `The implementation of artificial intelligence systems requires comprehensive analysis and systematic evaluation of all relevant factors. Furthermore, it is essential to ensure that the deployment process follows established protocols. Moreover, the optimization of performance metrics must be thoroughly evaluated. Additionally, comprehensive testing procedures should be implemented to validate system functionality.`,
        expectedImprovement: "DeepThink should identify and transform multiple AI patterns",
        aiRiskLevel: "EXTREME"
    },
    {
        name: "Corporate Jargon Test",
        text: `It is important to note that our organization's strategic initiatives will leverage synergistic opportunities to optimize operational efficiency. The comprehensive framework will facilitate enhanced collaboration across all stakeholders. Furthermore, the implementation of best practices will ensure sustainable growth and competitive advantage.`,
        expectedImprovement: "DeepThink should humanize corporate speak with natural language",
        aiRiskLevel: "HIGH"
    },
    {
        name: "Academic Writing Pattern",
        text: `This study examines the relationship between various factors and their impact on outcomes. The analysis reveals significant correlations that warrant further investigation. The findings suggest that implementation of the proposed methodology would result in improved performance metrics.`,
        expectedImprovement: "DeepThink should add personal voice to academic tone",
        aiRiskLevel: "MEDIUM"
    },
    {
        name: "Technical Documentation",
        text: `The API endpoint configuration requires proper authentication headers and CORS settings. Database connections should utilize connection pooling for optimal performance. Error handling mechanisms must be implemented to ensure system reliability and maintainability.`,
        expectedImprovement: "DeepThink should maintain accuracy while adding human perspective",
        aiRiskLevel: "MEDIUM"
    }
];

/**
 * Test DeepSeek-R1 with DeepThink vs without DeepThink
 */
async function testDeepThinkComparison() {
    console.log('🧠 DEEPSEEK-R1 DEEPTHINK COMPARISON TEST');
    console.log('═'.repeat(70));
    
    if (!isAdvancedLLMAvailable()) {
        console.log('❌ No DeepSeek-R1 API keys configured');
        console.log('   Please set up FIREWORKS_API_KEY, NOVITA_API_KEY, or OPENROUTER_API_KEY');
        return;
    }

    for (const testCase of deepThinkTestCases) {
        console.log(`\n🧪 TEST: ${testCase.name} (${testCase.aiRiskLevel} AI Risk)`);
        console.log('═'.repeat(50));
        
        console.log('\n📄 ORIGINAL TEXT:');
        console.log(testCase.text);
        
        // Analyze original AI patterns
        const originalAnalysis = analyzeAIPatterns(testCase.text);
        console.log(`\n🔍 Original AI Score: ${originalAnalysis.score}%`);
        
        // Test WITHOUT DeepThink
        console.log('\n🤖 Testing WITHOUT DeepThink...');
        const withoutDeepThink = await testHumanization(testCase.text, false);
        
        // Test WITH DeepThink
        console.log('\n🧠 Testing WITH DeepThink...');
        const withDeepThink = await testHumanization(testCase.text, true);
        
        // Compare results
        console.log('\n📊 COMPARISON RESULTS:');
        console.log('─'.repeat(40));
        
        if (withoutDeepThink.success && withDeepThink.success) {
            const withoutAnalysis = analyzeAIPatterns(withoutDeepThink.text);
            const withAnalysis = analyzeAIPatterns(withDeepThink.text);
            
            console.log(`Without DeepThink: ${withoutAnalysis.score}% AI detection`);
            console.log(`With DeepThink:    ${withAnalysis.score}% AI detection`);
            
            const improvement = withoutAnalysis.score - withAnalysis.score;
            console.log(`Improvement:       ${improvement > 0 ? '+' : ''}${improvement.toFixed(1)}% better`);
            
            // Check reasoning validation
            if (withDeepThink.reasoningValidation) {
                const rv = withDeepThink.reasoningValidation;
                console.log(`\n🧠 DeepThink Analysis:`);
                console.log(`   Reasoning Found: ${rv.hasReasoning ? '✅' : '❌'}`);
                console.log(`   Reasoning Quality: ${(rv.reasoningQuality * 100).toFixed(1)}%`);
                console.log(`   Reasoning Steps: ${rv.reasoningSteps}`);
                console.log(`   Reasoning Length: ${rv.reasoningLength} chars`);
            }
            
            // Quality assessment
            if (improvement > 5) {
                console.log('🎉 EXCELLENT: DeepThink shows significant improvement');
            } else if (improvement > 0) {
                console.log('✅ GOOD: DeepThink shows improvement');
            } else {
                console.log('⚠️  CONCERN: DeepThink not showing expected improvement');
            }
            
        } else {
            console.log('❌ One or both tests failed');
            if (!withoutDeepThink.success) console.log(`   Without DeepThink: ${withoutDeepThink.error}`);
            if (!withDeepThink.success) console.log(`   With DeepThink: ${withDeepThink.error}`);
        }
        
        console.log('\n' + '─'.repeat(50));
    }
}

/**
 * Test humanization with specific DeepThink setting
 */
async function testHumanization(text, enableDeepThink) {
    try {
        const result = await humanizeWithAdvancedLLM(text, {
            aggressiveness: 0.9,
            maintainTone: true,
            targetDetection: 10,
            preferredModel: 'deepseek-r1',
            enableDeepThink: enableDeepThink
        });
        
        if (result.success) {
            console.log(`   ✅ Success (${result.processingTime}ms)`);
            console.log(`   Model: ${result.provider}/${result.modelName || 'deepseek-r1'}`);
            if (enableDeepThink && result.reasoningValidation) {
                console.log(`   Reasoning: ${result.reasoningValidation.hasReasoning ? 'Found' : 'Missing'}`);
            }
            return result;
        } else {
            console.log(`   ❌ Failed: ${result.error}`);
            return result;
        }
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * Test the public API to ensure DeepThink is activated
 */
async function testPublicAPIDeepThink() {
    console.log('\n\n🌐 PUBLIC API DEEPTHINK TEST');
    console.log('═'.repeat(50));
    
    const testText = "The implementation of this solution requires comprehensive analysis and systematic evaluation of all relevant factors.";
    
    try {
        const result = await humanizeText(testText, {
            aggressiveness: 0.8,
            maintainTone: true,
            targetDetection: 10,
            method: 'llm' // Force LLM mode to use DeepSeek-R1
        });
        
        if (result.success) {
            console.log('✅ Public API Success');
            console.log(`Method: ${result.actualMethod}`);
            console.log(`Processing Time: ${result.totalProcessingTime}ms`);
            console.log(`Original: ${testText}`);
            console.log(`Humanized: ${result.humanizedText}`);
            
            // Check if DeepThink reasoning was used
            if (result.reasoningValidation) {
                console.log(`\n🧠 DeepThink Status: ${result.reasoningValidation.hasReasoning ? 'ACTIVE' : 'INACTIVE'}`);
            } else {
                console.log('\n⚠️  No reasoning validation data found');
            }
        } else {
            console.log(`❌ Public API Failed: ${result.error}`);
        }
    } catch (error) {
        console.log(`❌ Public API Error: ${error.message}`);
    }
}

/**
 * Run all DeepThink validation tests
 */
async function runDeepThinkValidation() {
    console.log('🚀 DEEPSEEK-R1 DEEPTHINK VALIDATION SUITE');
    console.log('═'.repeat(70));
    
    // Check provider status
    console.log('\n📊 PROVIDER STATUS:');
    const providerStatus = getProviderStatus();
    Object.entries(providerStatus).forEach(([model, providers]) => {
        if (model === 'deepseek-r1') {
            console.log(`\n${model}:`);
            providers.forEach(provider => {
                console.log(`  ${provider.name}: ${provider.available ? '✅' : '❌'} (${provider.model})`);
            });
        }
    });
    
    try {
        await testDeepThinkComparison();
        await testPublicAPIDeepThink();
        
        console.log('\n\n🎯 VALIDATION COMPLETE');
        console.log('═'.repeat(50));
        console.log('If DeepThink is working correctly, you should see:');
        console.log('✅ Reasoning chains found in DeepSeek-R1 responses');
        console.log('✅ Better AI detection scores with DeepThink enabled');
        console.log('✅ Detailed reasoning validation logs');
        console.log('✅ Natural, human-like humanized text output');
        
    } catch (error) {
        console.error('❌ Validation suite error:', error.message);
    }
}

// Run the validation
runDeepThinkValidation().catch(console.error);
