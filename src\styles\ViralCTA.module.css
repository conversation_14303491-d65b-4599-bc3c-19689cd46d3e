.viralCTA {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin: 3rem 0;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.viralCTA.floating {
  position: fixed;
  bottom: 15px;
  right: 15px;
  max-width: 240px; /* Significantly reduced from 350px */
  padding: 0.75rem; /* Reduced from 2rem */
  z-index: 999; /* Slightly lower to not interfere with main content */
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.5s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2); /* Reduced shadow */
  border-radius: 12px; /* Smaller border radius for compact look */
  /* Ensure it doesn't interfere with scrollbars or page edges */
  margin-right: 5px;
  margin-bottom: 5px;
  /* Prevent text selection and ensure it's above content but not intrusive */
  user-select: none;
  pointer-events: auto;
}

.viralCTA.floating.visible {
  transform: translateY(0);
  opacity: 1;
}

/* Compact styles for floating variant */
.viralCTA.floating .container {
  padding: 0; /* Remove extra container padding */
}

.viralCTA.floating .content {
  gap: 0.5rem; /* Reduced gap between elements */
}

.viralCTA.floating .header {
  text-align: center;
  margin-bottom: 0.5rem; /* Reduced margin */
}

.viralCTA.floating .emoji {
  font-size: 1.5rem; /* Reduced from default */
  margin-bottom: 0.25rem;
}

.viralCTA.floating .title {
  font-size: 0.9rem; /* Much smaller title */
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.viralCTA.floating .subtitle {
  font-size: 0.75rem; /* Smaller subtitle */
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  opacity: 0.9;
}

.viralCTA.floating .stats {
  display: none; /* Hide stats in floating version to save space */
}

.viralCTA.floating .actions {
  margin-top: 0.5rem;
}

.viralCTA.floating .button {
  padding: 0.4rem 0.8rem; /* Smaller button padding */
  font-size: 0.75rem; /* Smaller button text */
  font-weight: 600;
  border-radius: 6px;
  width: 100%;
}

.viralCTA.floating .socialShare {
  margin-top: 0.5rem;
  gap: 0.25rem; /* Reduced gap between social buttons */
}

.viralCTA.floating .socialButton {
  width: 28px; /* Smaller social buttons */
  height: 28px;
  font-size: 0.75rem;
}

.viralCTA.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.viralCTA::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="celebration" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="25" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="25" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><polygon points="35,15 37,20 42,20 38,24 40,29 35,26 30,29 32,24 28,20 33,20" fill="rgba(255,255,255,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23celebration)"/></svg>') repeat;
  opacity: 0.3;
}

.container {
  position: relative;
  z-index: 1;
}

.content {
  max-width: 600px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.stats {
  display: flex;
  justify-content: space-around;
  align-items: stretch;
  gap: 0.5rem;
  margin: 2rem 0;
  padding: 1.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-wrap: nowrap;
}

.stat {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.25rem;
  min-width: 0;
}

.statNumber {
  font-size: 1.6rem;
  font-weight: 800;
  display: block;
  margin-bottom: 0.3rem;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  text-align: center;
  width: 100%;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  font-weight: 500;
  line-height: 1.3;
  text-align: center;
  width: 100%;
  display: block;
}

.actions {
  margin: 2rem 0;
}

.primaryButton {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  color: white;
  border: none;
  padding: 1.2rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  margin-bottom: 2rem;
}

.primaryButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #ff5252 0%, #ffc107 100%);
}

.buttonIcon {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.primaryButton:hover .buttonIcon {
  transform: translateX(5px);
}

.socialProof {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.avatars {
  display: flex;
  gap: -0.5rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  border: 2px solid white;
  margin-left: -0.5rem;
}

.avatar:first-child {
  margin-left: 0;
}

.socialText {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.shareSection {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.viralFeatures {
  margin-top: 2rem;
  position: relative;
  z-index: 1;
}

.urgency {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.urgencyDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.urgencyText {
  font-weight: 500;
}

.testimonialQuote {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.testimonialQuote blockquote {
  font-size: 1rem;
  font-style: italic;
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
}

.testimonialQuote cite {
  font-size: 0.9rem;
  opacity: 0.8;
  font-style: normal;
}

/* Large desktop screens - slightly larger but still compact */
@media (min-width: 1400px) {
  .viralCTA.floating {
    max-width: 260px; /* Slightly larger on very large screens */
    padding: 0.85rem;
    bottom: 20px;
    right: 20px;
  }

  .viralCTA.floating .title {
    font-size: 0.95rem;
  }

  .viralCTA.floating .subtitle {
    font-size: 0.78rem;
  }
}

/* Tablet responsive styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .viralCTA.floating {
    bottom: 12px;
    right: 12px;
    max-width: 220px; /* Medium size for tablets */
    padding: 0.6rem;
  }

  .viralCTA.floating .title {
    font-size: 0.85rem;
  }

  .viralCTA.floating .subtitle {
    font-size: 0.72rem;
  }

  .viralCTA.floating .button {
    padding: 0.38rem 0.7rem;
    font-size: 0.72rem;
  }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .viralCTA {
    padding: 2rem 1rem;
    margin: 2rem 0;
  }
  
  .viralCTA.floating {
    bottom: 10px;
    right: 10px;
    left: auto; /* Don't stretch across full width */
    max-width: 200px; /* Even smaller on mobile */
    padding: 0.5rem; /* Very compact padding */
  }

  .viralCTA.floating .emoji {
    font-size: 1.2rem; /* Smaller emoji on mobile */
  }

  .viralCTA.floating .title {
    font-size: 0.8rem; /* Smaller title on mobile */
    line-height: 1.1;
  }

  .viralCTA.floating .subtitle {
    font-size: 0.7rem; /* Smaller subtitle on mobile */
    line-height: 1.2;
  }

  .viralCTA.floating .button {
    padding: 0.35rem 0.6rem; /* Even smaller button on mobile */
    font-size: 0.7rem;
  }

  .viralCTA.floating .socialButton {
    width: 24px; /* Smaller social buttons on mobile */
    height: 24px;
    font-size: 0.7rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .stats {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
    align-items: center;
  }

  .stat {
    flex: none;
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
    padding: 0.75rem 0;
  }

  .statNumber {
    font-size: 1.8rem;
    margin-bottom: 0.4rem;
  }

  .statLabel {
    font-size: 0.95rem;
    line-height: 1.4;
  }
  
  .primaryButton {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
  
  .avatars {
    gap: -0.25rem;
  }
  
  .avatar {
    width: 35px;
    height: 35px;
    font-size: 1rem;
    margin-left: -0.25rem;
  }
  
  .socialText {
    font-size: 0.8rem;
  }
}

/* Tablet breakpoint for better intermediate sizing */
@media (max-width: 1024px) and (min-width: 769px) {
  .stats {
    padding: 1.5rem 0.75rem;
    gap: 0.75rem;
  }

  .stat {
    padding: 0.5rem 0.2rem;
  }

  .statNumber {
    font-size: 1.4rem;
  }

  .statLabel {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}

@media (max-width: 480px) {
  .emoji {
    font-size: 2rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .stats {
    flex-direction: row;
    justify-content: space-between;
    gap: 0.25rem;
    padding: 1rem 0.5rem;
    margin: 1.5rem 0;
  }

  .stat {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 0.25rem 0.1rem;
  }

  .statNumber {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    line-height: 1.1;
  }

  .statLabel {
    font-size: 0.7rem;
    line-height: 1.2;
    padding: 0;
    word-break: break-word;
  }
}
