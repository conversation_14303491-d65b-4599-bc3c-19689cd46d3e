/**
 * Comprehensive Test Suite for Redesigned GhostLayer Humanization System
 * Tests the enhanced implementation for consistent ≤10% AI detection achievement
 */

import { humanizeWithAdvancedLLM } from './src/services/falconService.js';

// Test samples with varying AI detection risk levels
const testSamples = [
    {
        name: "EXTREME AI Risk - Academic Paper Style",
        text: "Furthermore, it is important to note that the comprehensive analysis demonstrates significant improvements in the implementation of this extensive methodology. Moreover, the substantial results indicate that this approach facilitates optimal outcomes while maintaining the highest standards of quality. Consequently, the findings suggest that this framework establishes a paradigm for future research endeavors.",
        expectedDetection: "95%+ before transformation",
        targetDetection: 5
    },
    {
        name: "HIGH AI Risk - Corporate Report Style", 
        text: "The organization has implemented a comprehensive strategy to optimize operational efficiency. This methodology demonstrates substantial improvements across various metrics. Additionally, the analysis indicates that these initiatives facilitate enhanced performance while maintaining quality standards.",
        expectedDetection: "80%+ before transformation",
        targetDetection: 8
    },
    {
        name: "MEDIUM AI Risk - Technical Documentation",
        text: "This system utilizes advanced algorithms to process data efficiently. The implementation provides significant benefits including improved performance and reduced processing time. Users can leverage these capabilities to enhance their workflow.",
        expectedDetection: "60%+ before transformation", 
        targetDetection: 10
    },
    {
        name: "LOW AI Risk - Conversational Content",
        text: "I think this approach works really well for most situations. It's pretty straightforward to implement and doesn't require too much technical knowledge. The results are quite good and it's easy to understand how everything fits together.",
        expectedDetection: "30%+ before transformation",
        targetDetection: 10
    }
];

/**
 * Test the redesigned humanization system
 */
async function testRedesignedSystem() {
    console.log('🚀 TESTING REDESIGNED GHOSTLAYER HUMANIZATION SYSTEM');
    console.log('=' .repeat(80));
    console.log('🎯 Target: Consistently achieve ≤10% AI detection with enhanced quality');
    console.log('🔬 Testing: Nuclear-level transformation with quality preservation');
    console.log('');

    const results = [];

    for (const sample of testSamples) {
        console.log(`\n📋 TEST CASE: ${sample.name}`);
        console.log('─'.repeat(60));
        
        console.log('\n📄 Original Text:');
        console.log(`"${sample.text}"`);
        
        console.log(`\n🎯 Expected Detection: ${sample.expectedDetection}`);
        console.log(`🎯 Target Detection: ≤${sample.targetDetection}%`);
        
        try {
            console.log('\n🔄 Processing with redesigned nuclear humanization...');
            
            const startTime = Date.now();
            const result = await humanizeWithAdvancedLLM(sample.text, {
                aggressiveness: 0.9, // Maximum aggressiveness
                maintainTone: false, // Allow full transformation
                targetDetection: sample.targetDetection,
                preferredModel: 'deepseek-r1',
                enableDeepThink: true,
                passNumber: 1,
                textLength: sample.text.length
            });
            const processingTime = Date.now() - startTime;
            
            if (result.success) {
                console.log('\n✅ TRANSFORMATION SUCCESSFUL');
                console.log('─'.repeat(40));
                
                console.log('\n🎭 Humanized Result:');
                console.log(`"${result.text}"`);
                
                console.log('\n📊 PERFORMANCE METRICS:');
                console.log(`⏱️  Processing Time: ${processingTime}ms`);
                console.log(`🤖 Model: ${result.modelName} (${result.method})`);
                console.log(`🔄 Multi-pass: ${result.multiPass ? 'YES' : 'NO'}`);
                if (result.totalPasses) {
                    console.log(`📈 Total Passes: ${result.totalPasses}`);
                }
                
                console.log('\n🎯 QUALITY VALIDATION:');
                if (result.qualityValidation) {
                    const qv = result.qualityValidation;
                    console.log(`📈 Composite Score: ${qv.compositeScore}/100`);
                    console.log(`🧠 Perplexity: ${qv.perplexity.score}/100 (${qv.perplexity.quality})`);
                    console.log(`⚠️  AI Risk Score: ${qv.aiAnalysis.riskScore}/20`);
                    console.log(`✅ Overall Quality: ${qv.overallQuality ? 'PASS' : 'FAIL'}`);
                }
                
                console.log('\n📚 ENHANCED QUALITY CHECKS:');
                if (result.readabilityCheck) {
                    console.log(`📖 Readability: ${result.readabilityCheck.score}/100 (${result.readabilityCheck.level})`);
                }
                if (result.transformationCheck) {
                    console.log(`🔄 Transformation Rate: ${result.transformationCheck.rate}%`);
                    console.log(`🎯 Semantic Preservation: ${result.transformationCheck.semanticPreservation}%`);
                    console.log(`📏 Length Change: ${result.transformationCheck.lengthChange}%`);
                }
                if (result.professionalToneCheck) {
                    console.log(`👔 Professional Tone: ${result.professionalToneCheck.maintained ? 'MAINTAINED' : 'COMPROMISED'}`);
                    console.log(`📊 Professional Density: ${result.professionalToneCheck.professionalDensity}%`);
                }
                
                if (result.overallQualityScore) {
                    console.log(`🏆 Overall Quality Score: ${result.overallQualityScore}/100`);
                }
                
                console.log('\n🔍 AI DETECTION VALIDATION:');
                if (result.detectionValidation) {
                    const dv = result.detectionValidation;
                    console.log(`🤖 Detection Score: ${dv.score?.toFixed(1) || 'N/A'}%`);
                    console.log(`🎯 Meets Target (≤${sample.targetDetection}%): ${dv.meetsTarget ? '✅ YES' : '❌ NO'}`);
                    if (dv.apiName) {
                        console.log(`🔧 API Used: ${dv.apiName}`);
                    }
                } else {
                    console.log('🔧 Using heuristic validation (no API available)');
                }
                
                // Calculate transformation metrics
                const originalWords = sample.text.split(/\s+/).length;
                const transformedWords = result.text.split(/\s+/).length;
                const lengthChange = ((transformedWords - originalWords) / originalWords * 100).toFixed(1);
                
                console.log('\n📈 TRANSFORMATION ANALYSIS:');
                console.log(`📝 Original Length: ${originalWords} words`);
                console.log(`📝 Transformed Length: ${transformedWords} words`);
                console.log(`📊 Length Change: ${lengthChange}%`);
                
                // Success criteria evaluation
                const successCriteria = {
                    qualityScore: result.overallQualityScore >= 70,
                    readability: result.readabilityCheck?.score >= 70,
                    transformation: result.transformationCheck?.rate >= 60,
                    professionalTone: result.professionalToneCheck?.maintained !== false,
                    aiDetection: result.detectionValidation?.meetsTarget !== false
                };
                
                const passedCriteria = Object.values(successCriteria).filter(Boolean).length;
                const totalCriteria = Object.keys(successCriteria).length;
                
                console.log('\n🏆 SUCCESS CRITERIA EVALUATION:');
                console.log(`✅ Quality Score ≥70: ${successCriteria.qualityScore ? 'PASS' : 'FAIL'}`);
                console.log(`✅ Readability ≥70: ${successCriteria.readability ? 'PASS' : 'FAIL'}`);
                console.log(`✅ Transformation ≥60%: ${successCriteria.transformation ? 'PASS' : 'FAIL'}`);
                console.log(`✅ Professional Tone: ${successCriteria.professionalTone ? 'PASS' : 'FAIL'}`);
                console.log(`✅ AI Detection Target: ${successCriteria.aiDetection ? 'PASS' : 'FAIL'}`);
                console.log(`🎯 Overall Success: ${passedCriteria}/${totalCriteria} criteria met`);
                
                results.push({
                    testCase: sample.name,
                    success: true,
                    processingTime,
                    qualityScore: result.overallQualityScore,
                    criteriaScore: `${passedCriteria}/${totalCriteria}`,
                    result
                });
                
            } else {
                console.log('\n❌ TRANSFORMATION FAILED');
                console.log(`Error: ${result.error}`);
                
                results.push({
                    testCase: sample.name,
                    success: false,
                    error: result.error
                });
            }
            
        } catch (error) {
            console.log('\n💥 TEST ERROR');
            console.log(`Error: ${error.message}`);
            
            results.push({
                testCase: sample.name,
                success: false,
                error: error.message
            });
        }
        
        console.log('\n' + '='.repeat(80));
    }
    
    // Final summary
    console.log('\n🎉 REDESIGNED SYSTEM TEST SUMMARY');
    console.log('=' .repeat(80));
    
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);
    
    console.log(`✅ Successful Tests: ${successfulTests.length}/${results.length}`);
    console.log(`❌ Failed Tests: ${failedTests.length}/${results.length}`);
    
    if (successfulTests.length > 0) {
        const avgQuality = successfulTests.reduce((sum, r) => sum + (r.qualityScore || 0), 0) / successfulTests.length;
        const avgProcessingTime = successfulTests.reduce((sum, r) => sum + r.processingTime, 0) / successfulTests.length;
        
        console.log(`📊 Average Quality Score: ${avgQuality.toFixed(1)}/100`);
        console.log(`⏱️  Average Processing Time: ${avgProcessingTime.toFixed(0)}ms`);
    }
    
    console.log('\n🚀 REDESIGNED SYSTEM ENHANCEMENTS:');
    console.log('✅ Nuclear-level AI pattern elimination');
    console.log('✅ Enhanced DeepSeek-R1 integration with optimized parameters');
    console.log('✅ Advanced multi-pass refinement with adaptive strategies');
    console.log('✅ Sophisticated adversarial attack patterns');
    console.log('✅ Contextual synonym replacement system');
    console.log('✅ Enhanced perplexity validation with stricter thresholds');
    console.log('✅ Comprehensive quality control mechanisms');
    console.log('✅ Professional readability preservation');
    
    console.log('\n🎯 EXPECTED IMPROVEMENTS:');
    console.log('• Consistent ≤10% AI detection achievement');
    console.log('• 75%+ word transformation rate');
    console.log('• Maintained professional tone and readability');
    console.log('• Enhanced natural language flow');
    console.log('• Commercial-grade quality assurance');
    
    return results;
}

// Run the comprehensive test
testRedesignedSystem().catch(console.error);
