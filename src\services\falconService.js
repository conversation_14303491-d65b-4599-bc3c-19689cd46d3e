/**
 * NLTK-Inspired Humanization Service
 * Reliable text humanization using JavaScript NLP techniques
 * Optimized for consistent performance without external API dependencies
 */

// Performance tiers for different user types
const PERFORMANCE_TIERS = {
    free: {
        processingDelay: 1000, // 1 second delay for free users
        cacheEnabled: false,
        parallelProcessing: false,
        useOptimizedRegex: false,
        usePrecomputedSets: false,
        algorithmComplexity: 'basic',
        batchSize: 1
    },
    premium: {
        processingDelay: 500, // 0.5 second delay for premium users
        cacheEnabled: true,
        parallelProcessing: true,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        algorithmComplexity: 'optimized',
        batchSize: 3
    },
    admin: {
        processingDelay: 0, // No delay for admin/owner
        cacheEnabled: true,
        parallelProcessing: true,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        algorithmComplexity: 'maximum',
        batchSize: 5
    }
};

// Optimized caches for performance tiers
const optimizedCaches = {
    word: new Map(),
    synonym: new Map(),
    pos: new Map(),
    sentence: new Map()
};

// Optimized regex patterns for premium/admin users
const optimizedRegexPatterns = {
    sentences: /[^.!?]*[.!?]+\s*|[^.!?]+$/g,
    tokens: /\w+|[^\w\s]+/g,
    words: /\w+/g,
    wordToken: /^\w+$/,
    wordStart: /^\w/,
    wordEnd: /\w$/,
    adverb: /ly$|ward$|wise$/,
    adjective: /ful$|less$|ous$|ive$|able$|ible$|al$|ic$|ed$|ing$/,
    verb: /ed$|ing$|s$/
};

// Common words that should not be replaced
const commonWordsSet = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
    'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
    'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
    'her', 'its', 'our', 'their'
]);

// User tier detection function
function getUserTier(options = {}) {
    // Check for admin/owner privileges
    if (options.isAdmin || options.isOwner || options.userTier === 'admin') {
        return 'admin';
    }
    
    // Check for premium subscription
    if (options.isPremium || options.userTier === 'premium') {
        return 'premium';
    }
    
    // Default to free tier
    return 'free';
}

/**
 * NLTK-inspired humanization using JavaScript NLP techniques
 * Now with advanced performance optimizations and user tier support
 */
export async function humanizeWithNLTKApproach(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        useAdvancedSynonyms = true,
        user = null // User session for tier detection
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    const userTier = getUserTier({ user, ...options });
    const performanceConfig = PERFORMANCE_TIERS[userTier];

    try {
        console.log(`🔬 Starting NLTK-inspired humanization (${userTier} tier)...`);

        // Apply processing delay for free users (feature limitation)
        if (performanceConfig.processingDelay > 0) {
            console.log(`⏳ Processing delay: ${performanceConfig.processingDelay}ms for ${userTier} users...`);
            await new Promise(resolve => setTimeout(resolve, performanceConfig.processingDelay));
        }

        // Apply NLTK-inspired processing with tier-specific optimizations
        const humanizedText = await processTextWithNLTKApproachOptimized(text, {
            aggressiveness,
            maintainTone,
            useAdvancedSynonyms,
            performanceConfig,
            userTier
        });

        const processingTime = Date.now() - startTime;

        return {
            success: true,
            text: humanizedText,
            originalText: text,
            method: 'nltk-optimized',
            processingTime,
            userTier,
            aggressiveness,
            targetDetection,
            wordCount: text.split(/\s+/).length,
            transformationRate: calculateTransformationRate(text, humanizedText)
        };

    } catch (error) {
        console.error('NLTK humanization error:', error.message);
        return {
            success: false,
            error: error.message,
            originalText: text,
            method: 'nltk-optimized',
            processingTime: Date.now() - startTime
        };
    }
}

/**
 * Calculate transformation rate between original and humanized text
 */
function calculateTransformationRate(original, humanized) {
    const originalWords = original.toLowerCase().split(/\s+/);
    const humanizedWords = humanized.toLowerCase().split(/\s+/);
    
    if (originalWords.length !== humanizedWords.length) {
        return Math.abs(originalWords.length - humanizedWords.length) / Math.max(originalWords.length, humanizedWords.length);
    }
    
    let changedWords = 0;
    for (let i = 0; i < originalWords.length; i++) {
        if (originalWords[i] !== humanizedWords[i]) {
            changedWords++;
        }
    }
    
    return changedWords / originalWords.length;
}

/**
 * Check if advanced LLM service is available (always false for NLTK-only version)
 */
export function isAdvancedLLMAvailable() {
    return false; // NLTK-only version doesn't use external APIs
}

/**
 * Get status of available providers (empty for NLTK-only version)
 */
export function getProviderStatus() {
    return {
        nltk: {
            available: true,
            method: 'local-javascript',
            description: 'NLTK-inspired JavaScript NLP processing'
        }
    };
}

/**
 * Performance-optimized NLTK-inspired text processing with advanced caching and parallel processing
 */
async function processTextWithNLTKApproachOptimized(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        useAdvancedSynonyms = true,
        performanceConfig = PERFORMANCE_TIERS.free,
        userTier = 'free'
    } = options;

    // Use optimized regex patterns for premium/admin users
    const sentenceRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.sentences : /[^.!?]*[.!?]+\s*|[^.!?]+$/g;
    const tokenRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.tokens : /\w+|[^\w\s]+/g;

    // Preserve newlines with placeholder
    const newlinePlaceholder = "庄周";
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    // Split into sentences using optimized regex
    const sentences = textWithPlaceholders.match(sentenceRegex) || [textWithPlaceholders];

    // Use parallel processing for premium/admin users with larger texts
    if (performanceConfig.parallelProcessing && sentences.length > 3) {
        return await processTextParallel(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    } else {
        return await processTextSequential(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    }
}

/**
 * Parallel processing for premium/admin users - processes sentences concurrently
 */
async function processTextParallel(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    // Process sentences in parallel batches
    const batchSize = performanceConfig.batchSize;
    const batches = [];

    for (let i = 0; i < sentences.length; i += batchSize) {
        batches.push(sentences.slice(i, i + batchSize));
    }

    const processedBatches = await Promise.all(batches.map(async (batch) => {
        return await Promise.all(batch.map(async (sentence) => {
            return await processSingleSentenceOptimized(sentence, {
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig,
                userTier,
                tokenRegex
            });
        }));
    }));

    // Flatten batches and join results
    const humanizedSentences = processedBatches.flat();
    let result = humanizedSentences.join('');

    // Final cleanup and restore newlines
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Sequential processing for free users - processes sentences one by one
 */
async function processTextSequential(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    const humanizedSentences = [];

    for (const sentence of sentences) {
        const processedSentence = await processSingleSentenceOptimized(sentence, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex
        });
        humanizedSentences.push(processedSentence);
    }

    // Join results and cleanup
    let result = humanizedSentences.join('');
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Optimized single sentence processing with caching and performance enhancements
 */
async function processSingleSentenceOptimized(sentence, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex } = options;

    if (!sentence.trim()) {
        return sentence;
    }

    // Check sentence cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        const cached = optimizedCaches.sentence.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Tokenize sentence using optimized regex
    const tokens = sentence.match(tokenRegex) || [];

    // Get POS tags with caching
    const posTags = getPOSTagsOptimized(sentence, performanceConfig);

    // Process tokens with optimized replacement
    const humanizedTokens = [];
    let wordIndex = 0;

    for (const token of tokens) {
        if (optimizedRegexPatterns.wordToken.test(token)) {
            const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
            const humanizedWord = await replaceWordWithPOSOptimized(
                token,
                posTag.pos,
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig
            );
            humanizedTokens.push(humanizedWord);
            wordIndex++;
        } else {
            humanizedTokens.push(token);
        }
    }

    // Reconstruct sentence with optimized spacing
    let humanizedSentence = reconstructSentenceOptimized(humanizedTokens, performanceConfig);

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        optimizedCaches.sentence.set(cacheKey, humanizedSentence);
    }

    return humanizedSentence;
}

/**
 * Optimized POS tagging with caching and performance enhancements
 */
function getPOSTagsOptimized(sentence, performanceConfig) {
    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.pos.get(sentence);
        if (cached) {
            return cached;
        }
    }

    // Use optimized regex for premium/admin users
    const wordRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.words : /\w+/g;

    const words = sentence.match(wordRegex) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTagOptimized(word.toLowerCase(), performanceConfig);
        tags.push({ word, pos });
    }

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.pos.set(sentence, tags);
    }

    return tags;
}

/**
 * Optimized POS tag determination with pre-compiled patterns and caching
 */
function determinePOSTagOptimized(word, performanceConfig) {
    // Use optimized regex patterns for premium/admin users
    if (performanceConfig.useOptimizedRegex) {
        // Adverb patterns (RB) - check first as they're more specific
        if (optimizedRegexPatterns.adverb.test(word)) return 'RB';

        // Adjective patterns (JJ)
        if (optimizedRegexPatterns.adjective.test(word)) return 'JJ';

        // Verb patterns (VB)
        if (optimizedRegexPatterns.verb.test(word)) return 'VB';

        // Common adverbs (pre-computed set for O(1) lookup)
        if (performanceConfig.usePrecomputedSets && commonAdverbsSet.has(word)) return 'RB';

        // Common adjectives (pre-computed set for O(1) lookup)
        if (performanceConfig.usePrecomputedSets && commonAdjectivesSet.has(word)) return 'JJ';
    }

    // Fallback to basic pattern matching
    return determinePOSTag(word);
}

// Pre-computed sets for O(1) lookups (premium/admin optimization)
const commonAdverbsSet = new Set([
    'very', 'really', 'quite', 'rather', 'extremely', 'incredibly', 'remarkably', 'exceptionally',
    'particularly', 'especially', 'quickly', 'rapidly', 'swiftly', 'slowly', 'gradually',
    'often', 'frequently', 'usually', 'normally', 'typically', 'generally', 'commonly',
    'sometimes', 'occasionally', 'rarely', 'seldom', 'never', 'always', 'constantly',
    'clearly', 'obviously', 'evidently', 'apparently', 'definitely', 'certainly', 'probably'
]);

const commonAdjectivesSet = new Set([
    'good', 'bad', 'great', 'small', 'large', 'big', 'new', 'old', 'high', 'low',
    'important', 'different', 'possible', 'available', 'necessary', 'special', 'certain',
    'easy', 'hard', 'difficult', 'simple', 'complex', 'fast', 'slow', 'quick', 'strong',
    'weak', 'beautiful', 'ugly', 'happy', 'sad', 'young', 'early', 'late', 'recent'
]);

/**
 * Determine POS tag for a word using pattern matching
 */
function determinePOSTag(word) {
    // Adjective patterns (JJ)
    if (word.match(/^(very|quite|rather|extremely|highly|incredibly|amazingly|particularly|especially|remarkably)$/)) return 'RB';
    if (word.match(/(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/)) return 'JJ';
    if (word.match(/^(good|bad|great|small|large|big|new|old|high|low|important|different|possible|available|necessary|special|certain|clear|simple|easy|difficult|hard|strong|weak|beautiful|ugly|happy|sad|young|early|late|recent)$/)) return 'JJ';

    // Adverb patterns (RB)
    if (word.match(/(ly|ward|wise)$/)) return 'RB';
    if (word.match(/^(very|quite|rather|really|actually|basically|generally|usually|normally|typically|commonly|frequently|often|sometimes|occasionally|rarely|seldom|never|always|constantly|continuously|regularly|irregularly|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|originally|initially|finally|eventually|ultimately|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|mostly|mainly|primarily|chiefly|largely|generally|specifically|particularly|especially|notably|remarkably|significantly|considerably|substantially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally)$/)) return 'RB';

    // Verb patterns (VB)
    if (word.match(/(ed|ing|s)$/)) return 'VB';
    if (word.match(/^(is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|could|should|might|may|can|must|shall|ought|need|dare|used)$/)) return 'VB';

    // Noun patterns (NN) - default
    return 'NN';
}

/**
 * Optimized word replacement with advanced caching and performance tiers
 */
async function replaceWordWithPOSOptimized(word, posTag, aggressiveness, useAdvancedSynonyms, performanceConfig) {
    // Skip very short words or common words using optimized lookup
    if (word.length <= 2 || isCommonWordOptimized(word, performanceConfig)) {
        return word;
    }

    const cacheKey = `${word.toLowerCase()}_${posTag}_${aggressiveness}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.word.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3;

    if (posTag.startsWith('JJ')) {
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        replacementProbability = 0.4 * aggressiveness;
    } else {
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Get synonyms with optimized lookup (now async for Natural.js support)
    const synonyms = await getSynonymsByPOSOptimized(word.toLowerCase(), posTag, performanceConfig);

    if (synonyms.length === 0) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Choose synonym with optimized selection
    const chosenSynonym = selectBestSynonymOptimized(synonyms, word, performanceConfig);
    const result = preserveCapitalization(word, chosenSynonym);

    // Cache the result
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.word.set(cacheKey, result);
    }

    return result;
}

/**
 * Optimized common word checking with pre-computed sets
 */
function isCommonWordOptimized(word, performanceConfig) {
    if (performanceConfig.usePrecomputedSets) {
        return commonWordsSet.has(word.toLowerCase());
    }
    return isCommonWord(word);
}

/**
 * Check if word is too common to replace
 */
function isCommonWord(word) {
    return commonWordsSet.has(word.toLowerCase());
}

/**
 * Optimized synonym lookup with caching and performance tiers
 */
async function getSynonymsByPOSOptimized(word, posTag, performanceConfig) {
    const cacheKey = `synonym_${word}_${posTag}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.synonym.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Get synonyms based on algorithm complexity
    let synonyms = [];

    if (performanceConfig.algorithmComplexity === 'maximum') {
        synonyms = await getSynonymsByPOSAdvanced(word, posTag);
    } else if (performanceConfig.algorithmComplexity === 'optimized') {
        synonyms = getSynonymsByPOSStandard(word, posTag);
    } else {
        synonyms = getSynonymsByPOSBasic(word, posTag);
    }

    // Cache the result
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.synonym.set(cacheKey, synonyms);
    }

    return synonyms;
}

/**
 * Advanced synonym lookup (admin tier) - uses comprehensive synonym database
 */
async function getSynonymsByPOSAdvanced(word, posTag) {
    // This would integrate with Natural.js WordNet in the future
    // For now, use the comprehensive synonym database
    return getSynonymsByPOSStandard(word, posTag);
}

/**
 * Standard synonym lookup (premium tier) - uses curated synonym database
 */
function getSynonymsByPOSStandard(word, posTag) {
    const synonymDatabase = {
        // Adjectives (JJ)
        'important': ['key', 'crucial', 'vital', 'essential', 'significant', 'major', 'critical'],
        'good': ['great', 'excellent', 'fine', 'nice', 'wonderful', 'amazing', 'fantastic'],
        'bad': ['poor', 'terrible', 'awful', 'horrible', 'dreadful', 'lousy'],
        'big': ['large', 'huge', 'massive', 'enormous', 'giant', 'vast'],
        'small': ['tiny', 'little', 'mini', 'compact', 'minor'],
        'new': ['fresh', 'recent', 'modern', 'latest', 'current'],
        'old': ['ancient', 'aged', 'vintage', 'classic', 'traditional'],
        'different': ['various', 'distinct', 'unique', 'diverse', 'alternative'],
        'easy': ['simple', 'effortless', 'straightforward', 'basic'],
        'hard': ['difficult', 'tough', 'challenging', 'complex', 'demanding'],
        'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty'],
        'slow': ['gradual', 'sluggish', 'leisurely', 'delayed'],

        // Adverbs (RB)
        'very': ['extremely', 'highly', 'incredibly', 'remarkably', 'exceptionally'],
        'really': ['truly', 'genuinely', 'actually', 'honestly', 'seriously'],
        'quickly': ['rapidly', 'swiftly', 'speedily', 'hastily', 'promptly'],
        'slowly': ['gradually', 'leisurely', 'steadily', 'carefully'],
        'often': ['frequently', 'regularly', 'commonly', 'usually'],
        'sometimes': ['occasionally', 'periodically', 'now and then'],
        'always': ['constantly', 'continuously', 'perpetually', 'forever'],
        'never': ['not ever', 'at no time', 'under no circumstances'],

        // Verbs (VB)
        'make': ['create', 'produce', 'build', 'construct', 'generate'],
        'get': ['obtain', 'acquire', 'receive', 'gain', 'secure'],
        'give': ['provide', 'offer', 'supply', 'deliver', 'present'],
        'take': ['grab', 'seize', 'capture', 'obtain', 'acquire'],
        'use': ['utilize', 'employ', 'apply', 'operate', 'handle'],
        'show': ['display', 'demonstrate', 'reveal', 'exhibit', 'present'],
        'help': ['assist', 'aid', 'support', 'guide', 'facilitate'],
        'work': ['function', 'operate', 'perform', 'labor', 'toil'],
        'find': ['discover', 'locate', 'identify', 'detect', 'uncover'],
        'think': ['believe', 'consider', 'suppose', 'assume', 'imagine']
    };

    const synonyms = synonymDatabase[word.toLowerCase()] || [];

    // Filter by POS tag if needed
    if (posTag.startsWith('JJ') || posTag.startsWith('RB') || posTag.startsWith('VB')) {
        return synonyms;
    }

    return synonyms;
}

/**
 * Basic synonym lookup (free tier) - uses limited synonym database
 */
function getSynonymsByPOSBasic(word, posTag) {
    const basicSynonyms = {
        'good': ['great', 'nice', 'fine'],
        'bad': ['poor', 'terrible'],
        'big': ['large', 'huge'],
        'small': ['tiny', 'little'],
        'fast': ['quick', 'rapid'],
        'slow': ['gradual'],
        'very': ['extremely', 'highly'],
        'really': ['truly', 'actually'],
        'make': ['create', 'build'],
        'get': ['obtain', 'receive'],
        'use': ['utilize', 'employ'],
        'show': ['display', 'reveal']
    };

    return basicSynonyms[word.toLowerCase()] || [];
}

/**
 * Optimized synonym selection with performance considerations
 */
function selectBestSynonymOptimized(synonyms, originalWord, performanceConfig) {
    if (synonyms.length === 0) return originalWord;

    if (performanceConfig.algorithmComplexity === 'maximum') {
        // Advanced selection considering context and frequency
        return selectBestSynonymAdvanced(synonyms, originalWord);
    } else {
        // Simple random selection
        return synonyms[Math.floor(Math.random() * synonyms.length)];
    }
}

/**
 * Advanced synonym selection (admin tier)
 */
function selectBestSynonymAdvanced(synonyms, originalWord) {
    // Prefer synonyms that are similar in length to the original
    const originalLength = originalWord.length;
    const lengthSorted = synonyms.sort((a, b) => {
        const aDiff = Math.abs(a.length - originalLength);
        const bDiff = Math.abs(b.length - originalLength);
        return aDiff - bDiff;
    });

    // Return one of the top 3 best matches
    const topMatches = lengthSorted.slice(0, Math.min(3, lengthSorted.length));
    return topMatches[Math.floor(Math.random() * topMatches.length)];
}

/**
 * Preserve original capitalization pattern
 */
function preserveCapitalization(original, replacement) {
    if (!original || !replacement) return replacement;

    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }

    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }

    return replacement.toLowerCase();
}

/**
 * Optimized sentence reconstruction with performance considerations
 */
function reconstructSentenceOptimized(tokens, performanceConfig) {
    if (performanceConfig.algorithmComplexity === 'maximum') {
        return reconstructSentenceAdvanced(tokens);
    } else {
        return reconstructSentenceBasic(tokens);
    }
}

/**
 * Advanced sentence reconstruction (admin tier)
 */
function reconstructSentenceAdvanced(tokens) {
    let result = '';
    let needsSpace = false;

    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        const nextToken = tokens[i + 1];

        if (needsSpace && /^\w/.test(token)) {
            result += ' ';
        }

        result += token;

        // Determine if next token needs a space
        needsSpace = /\w$/.test(token) && nextToken && /^\w/.test(nextToken);
    }

    return result;
}

/**
 * Basic sentence reconstruction (free/premium tier)
 */
function reconstructSentenceBasic(tokens) {
    return tokens.join('');
}

/**
 * Optimized symbol cleanup with performance tiers
 */
function cleanSymbolsOptimized(text, performanceConfig) {
    if (performanceConfig.algorithmComplexity === 'maximum') {
        return cleanSymbolsAdvanced(text);
    } else {
        return cleanSymbolsBasic(text);
    }
}

/**
 * Advanced symbol cleanup (admin tier)
 */
function cleanSymbolsAdvanced(text) {
    return text
        .replace(/\s+([.!?,:;])/g, '$1')
        .replace(/([.!?])\s*([.!?])/g, '$1 $2')
        .replace(/\s+/g, ' ')
        .replace(/\s*\n\s*/g, '\n')
        .trim();
}

/**
 * Basic symbol cleanup (free/premium tier)
 */
function cleanSymbolsBasic(text) {
    return text
        .replace(/\s+([.!?,:;])/g, '$1')
        .replace(/\s+/g, ' ')
        .trim();
}

// Export the main function as default for backward compatibility
export default humanizeWithNLTKApproach;
