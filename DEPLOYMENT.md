# GhostLayer - Deployment Guide

This guide covers deploying Ghost<PERSON><PERSON><PERSON> to various platforms for production use.

## Prerequisites

- Node.js 18+ installed
- Database (PostgreSQL recommended for production)
- Environment variables configured
- Domain name (for production)

## Environment Variables

Copy `.env.example` to `.env.local` and configure all required variables:

### Required Variables
```bash
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXTAUTH_SECRET=your_super_strong_random_secret
NEXTAUTH_URL=https://yourdomain.com

# Database
DATABASE_URL=postgresql://user:password@host:port/database

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payments
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
PREMIUM_PLAN_PRICE_ID=price_your_premium_plan_id

# External Services
GPTZERO_API_KEY=your_gptzero_api_key
```

## Deployment Options

### 1. Vercel (Recommended)

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel --prod
   ```

2. **Configure Environment Variables**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all required environment variables
   - Ensure `DATABASE_URL` points to your production database

3. **Database Setup**
   ```bash
   # Run migrations
   npx prisma migrate deploy
   
   # Seed database (optional)
   npm run db:seed
   ```

### 2. Netlify

1. **Build Configuration**
   - Build command: `npm run build`
   - Publish directory: `.next`
   - Functions directory: `netlify/functions`

2. **Environment Variables**
   - Configure in Netlify Dashboard → Site Settings → Environment Variables

### 3. Docker Deployment

1. **Build Image**
   ```bash
   docker build -t stealthwriter-ai .
   ```

2. **Run with Docker Compose**
   ```bash
   # Update environment variables in docker-compose.yml
   docker-compose up -d
   ```

3. **Database Migration**
   ```bash
   docker-compose exec app npx prisma migrate deploy
   ```

### 4. Traditional VPS/Server

1. **Server Setup**
   ```bash
   # Install Node.js, PM2, and Nginx
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   npm install -g pm2
   sudo apt-get install nginx
   ```

2. **Application Setup**
   ```bash
   # Clone repository
   git clone <your-repo-url>
   cd stealthwriter-ai
   
   # Install dependencies
   npm ci --production
   
   # Build application
   npm run build
   
   # Run database migrations
   npx prisma migrate deploy
   
   # Start with PM2
   pm2 start ecosystem.config.js
   ```

3. **Nginx Configuration**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## Database Setup

### PostgreSQL (Production)

1. **Create Database**
   ```sql
   CREATE DATABASE stealthwriter_db;
   CREATE USER stealthwriter_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE stealthwriter_db TO stealthwriter_user;
   ```

2. **Update Environment**
   ```bash
   DATABASE_URL="postgresql://stealthwriter_user:secure_password@localhost:5432/stealthwriter_db"
   ```

3. **Run Migrations**
   ```bash
   npx prisma migrate deploy
   ```

## External Services Setup

### Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://yourdomain.com/api/auth/callback/google`

### Stripe Setup

1. Create [Stripe account](https://stripe.com/)
2. Get API keys from Dashboard
3. Create products and prices
4. Set up webhooks:
   - Endpoint: `https://yourdomain.com/api/stripe/webhook`
   - Events: `checkout.session.completed`, `invoice.payment_succeeded`, `customer.subscription.updated`

### GPTZero API

1. Sign up at [GPTZero](https://gptzero.me/)
2. Get API key from dashboard
3. Configure rate limits based on your plan

## Monitoring and Maintenance

### Health Checks

- Health endpoint: `https://yourdomain.com/api/health`
- Monitor database connectivity
- Check external service availability

### Logging

- Logs are written to `./logs/app.log`
- Error logs: `./logs/error.log`
- Configure log rotation in production

### Backup Strategy

1. **Database Backups**
   ```bash
   # PostgreSQL backup
   pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Automated Backups**
   ```bash
   # Add to crontab
   0 2 * * * /path/to/backup-script.sh
   ```

### Performance Optimization

1. **Enable Caching**
   - Configure Redis for session storage
   - Enable Next.js caching

2. **CDN Setup**
   - Use Vercel Edge Network or Cloudflare
   - Configure static asset caching

3. **Database Optimization**
   - Add database indexes
   - Configure connection pooling

## Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers enabled
- [ ] Regular security updates

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify database server is running
   - Check firewall settings

2. **Authentication Issues**
   - Verify Google OAuth configuration
   - Check NEXTAUTH_URL matches domain
   - Ensure NEXTAUTH_SECRET is set

3. **Payment Processing Issues**
   - Verify Stripe webhook endpoint
   - Check webhook secret configuration
   - Monitor Stripe dashboard for errors

### Support

For deployment issues:
1. Check application logs
2. Verify environment variables
3. Test health endpoint
4. Review external service status

## Scaling Considerations

### Horizontal Scaling

- Use load balancer (Nginx, HAProxy)
- Configure session storage (Redis)
- Database read replicas

### Vertical Scaling

- Monitor resource usage
- Optimize database queries
- Implement caching strategies

### Cost Optimization

- Monitor usage metrics
- Optimize API calls to external services
- Use appropriate instance sizes
- Implement efficient caching
