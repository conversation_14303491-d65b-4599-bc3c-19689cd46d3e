// Test current humanization quality directly
import { humanizeText } from './src/services/humaneyesService.js';

const testText = `Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for future applications. Furthermore, it is important to note that machine learning algorithms can effectively analyze vast amounts of data to identify patterns and make predictions. Additionally, these systems utilize sophisticated neural networks to process information in ways that mimic human cognitive processes.

Moreover, the implementation of AI technologies has resulted in substantial improvements in efficiency and accuracy across various sectors. It is worth mentioning that organizations are increasingly leveraging these capabilities to optimize their operations and enhance decision-making processes. Consequently, the integration of artificial intelligence represents a transformative shift in how businesses approach complex challenges.

In conclusion, the continued advancement of AI systems will undoubtedly lead to even more innovative solutions and applications. It should be noted that this technological evolution requires careful consideration of ethical implications and responsible development practices. Therefore, stakeholders must collaborate to ensure that artificial intelligence benefits society as a whole.`;

async function testCurrentQuality() {
    console.log('🧪 Testing Current Humanization Quality...\n');
    
    console.log('📝 Original text:');
    console.log(testText);
    console.log('\n' + '='.repeat(80) + '\n');
    
    try {
        // Test different methods to identify issues
        const methods = [
            { name: 'Auto (Default)', method: 'auto', target: 10 },
            { name: 'DeepSeek-R1 LLM', method: 'llm', target: 10 },
            { name: 'Pattern-Based', method: 'pattern', target: 10 },
            { name: 'NLTK Approach', method: 'nltk', target: 10 },
            { name: 'Commercial Grade', method: 'commercial', target: 5 }
        ];
        
        for (const testMethod of methods) {
            console.log(`\n🔬 Testing ${testMethod.name} Method (Target: ≤${testMethod.target}%):`);
            console.log('-'.repeat(60));
            
            const startTime = Date.now();
            const result = await humanizeText(testText, {
                aggressiveness: 0.8,
                maintainTone: true,
                targetDetection: testMethod.target,
                method: testMethod.method,
                fallbackEnabled: true
            });
            const processingTime = Date.now() - startTime;
            
            if (result.success) {
                console.log(`✅ Success: ${result.actualMethod || result.method}`);
                console.log(`⏱️  Processing Time: ${processingTime}ms`);
                
                const humanizedText = result.text || result.humanizedText;
                console.log(`📝 Result Length: ${humanizedText.length} chars (${testText.length} → ${humanizedText.length})`);
                
                // Analyze transformation quality
                const originalWords = testText.toLowerCase().split(/\s+/);
                const humanizedWords = humanizedText.toLowerCase().split(/\s+/);
                
                let changedWords = 0;
                const changes = [];
                
                for (let i = 0; i < Math.min(originalWords.length, humanizedWords.length); i++) {
                    if (originalWords[i] !== humanizedWords[i]) {
                        changedWords++;
                        changes.push({
                            original: originalWords[i],
                            humanized: humanizedWords[i]
                        });
                    }
                }
                
                const changePercentage = ((changedWords / originalWords.length) * 100).toFixed(1);
                console.log(`📊 Transformation: ${changedWords}/${originalWords.length} words changed (${changePercentage}%)`);
                
                // Show first few changes
                if (changes.length > 0) {
                    console.log('🔍 Sample Changes:');
                    changes.slice(0, 5).forEach((change, index) => {
                        console.log(`   ${index + 1}. "${change.original}" → "${change.humanized}"`);
                    });
                    if (changes.length > 5) {
                        console.log(`   ... and ${changes.length - 5} more changes`);
                    }
                }
                
                // Check AI pattern removal
                const aiPatterns = [
                    'furthermore', 'moreover', 'additionally', 'consequently',
                    'it is important to note', 'it should be noted', 'it is worth mentioning',
                    'substantial', 'significant', 'numerous', 'sophisticated'
                ];
                
                let patternsRemoved = 0;
                let totalPatterns = 0;
                
                aiPatterns.forEach(pattern => {
                    const originalCount = (testText.toLowerCase().match(new RegExp(pattern, 'g')) || []).length;
                    const humanizedCount = (humanizedText.toLowerCase().match(new RegExp(pattern, 'g')) || []).length;
                    if (originalCount > 0) {
                        totalPatterns++;
                        if (humanizedCount < originalCount) {
                            patternsRemoved++;
                        }
                    }
                });
                
                console.log(`🎯 AI Patterns: ${patternsRemoved}/${totalPatterns} removed`);
                
                // Quality assessment
                let qualityScore = 'UNKNOWN';
                if (changePercentage < 5) {
                    qualityScore = '❌ POOR (Insufficient transformation)';
                } else if (changePercentage < 15) {
                    qualityScore = '⚠️  FAIR (Moderate transformation)';
                } else if (changePercentage < 30) {
                    qualityScore = '✅ GOOD (Substantial transformation)';
                } else {
                    qualityScore = '🎉 EXCELLENT (Extensive transformation)';
                }
                
                console.log(`📈 Quality: ${qualityScore}`);
                
                // Show detection target info
                if (result.detectionTarget) {
                    console.log(`🎯 Target: ≤${result.detectionTarget}% AI detection`);
                }
                
                // Show a sample of the output
                console.log('📄 Sample Output:');
                console.log(humanizedText.substring(0, 200) + '...');
                
            } else {
                console.log(`❌ Failed: ${result.error}`);
                if (result.attempts) {
                    console.log('🔄 Attempts made:');
                    result.attempts.forEach((attempt, index) => {
                        console.log(`   ${index + 1}. ${attempt.method}: ${attempt.success ? '✅' : '❌'}`);
                    });
                }
            }
        }
        
    } catch (error) {
        console.error('❌ Error testing humanization:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testCurrentQuality().catch(console.error);
