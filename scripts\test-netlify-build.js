#!/usr/bin/env node

/**
 * Test script to verify Netlify build process works locally
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function testBuild() {
  console.log('🧪 Testing Netlify build process locally...');
  
  try {
    // Clean previous builds
    console.log('🧹 Cleaning previous builds...');
    if (fs.existsSync('.next')) {
      fs.rmSync('.next', { recursive: true, force: true });
    }
    if (fs.existsSync('out')) {
      fs.rmSync('out', { recursive: true, force: true });
    }

    // Set environment variables for testing
    process.env.NETLIFY = 'true';
    process.env.HUSKY = '0';
    process.env.NODE_ENV = 'production';

    // Test the build command that <PERSON>lify will use
    console.log('📦 Installing dependencies (ignoring scripts)...');
    execSync('npm ci --include=dev --ignore-scripts', { 
      stdio: 'inherit',
      env: { ...process.env }
    });

    console.log('🔧 Running postinstall script...');
    execSync('npm run postinstall', { 
      stdio: 'inherit',
      env: { ...process.env }
    });

    console.log('🏗️ Running Netlify build script...');
    execSync('npm run build:netlify', { 
      stdio: 'inherit',
      env: { ...process.env }
    });

    // Verify output
    if (fs.existsSync('out')) {
      const files = fs.readdirSync('out');
      console.log(`✅ Build successful! Generated ${files.length} files in output directory`);
      
      // Check for essential files
      const essentialFiles = ['index.html', '_next'];
      const missingFiles = essentialFiles.filter(file => !files.includes(file));
      
      if (missingFiles.length === 0) {
        console.log('✅ All essential files present');
      } else {
        console.warn('⚠️ Missing essential files:', missingFiles);
      }
    } else {
      console.error('❌ Output directory not created');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Build test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testBuild().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
