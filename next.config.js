
/** @type {import('next').NextConfig} */
const nextConfig = {
  // For Netlify, we need to keep API routes working, so no static export
  // Netlify will handle the deployment with functions
  trailingSlash: true,

  images: {
    unoptimized: true, // Always unoptimized for Netlify
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    esmExternals: true,
  },
  poweredByHeader: false,
  reactStrictMode: true,
  swcMinify: true,
};

module.exports = nextConfig;
