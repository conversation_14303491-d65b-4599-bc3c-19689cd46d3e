/**
 * Ultra-Aggressive Humanization Test Suite
 * Tests the enhanced DeepSeek-R1 system with dramatic text transformations
 * Validates against ZeroGPT and other AI detection tools
 */

import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable } from './src/services/falconService.js';
import { humanizeText } from './src/services/humaneyesService.js';

// Test cases with high AI detection risk
const ULTRA_AGGRESSIVE_TEST_CASES = [
    {
        name: "Corporate Jargon Nightmare",
        originalText: `The implementation of this comprehensive solution requires strategic optimization and systematic evaluation of all relevant factors. Furthermore, it is essential to ensure that the deployment process follows established protocols and leverages best practices. Moreover, the optimization of performance metrics must be thoroughly evaluated to facilitate enhanced operational efficiency.`,
        expectedTransformation: "Should completely eliminate corporate speak and formal language",
        targetDetection: 5,
        difficulty: "EXTREME"
    },
    {
        name: "Academic Formality Hell",
        originalText: `This study examines the relationship between various factors and their impact on organizational outcomes. The analysis reveals significant correlations that warrant further investigation. The findings suggest that implementation of the proposed methodology would result in improved performance metrics and enhanced operational effectiveness.`,
        expectedTransformation: "Should destroy all academic formality and add personal voice",
        targetDetection: 8,
        difficulty: "HIGH"
    },
    {
        name: "AI Pattern Overload",
        originalText: `Additionally, it is important to note that the systematic approach involves multiple sequential steps that must be carefully coordinated. Subsequently, comprehensive testing procedures should be implemented to validate system functionality. Therefore, it is essential to maintain rigorous quality assurance protocols throughout the entire process.`,
        expectedTransformation: "Should obliterate all AI transition words and formal structures",
        targetDetection: 10,
        difficulty: "EXTREME"
    },
    {
        name: "Technical Documentation Torture",
        originalText: `The API endpoint configuration requires proper authentication headers and CORS settings to ensure secure communication. Database connections should utilize connection pooling for optimal performance and resource management. Error handling mechanisms must be implemented to ensure system reliability and maintainability.`,
        expectedTransformation: "Should maintain technical accuracy while adding human casualness",
        targetDetection: 10,
        difficulty: "HIGH"
    }
];

/**
 * Test ultra-aggressive humanization with before/after comparison
 */
async function testUltraAggressiveHumanization() {
    console.log('🔥 ULTRA-AGGRESSIVE HUMANIZATION TEST SUITE');
    console.log('═'.repeat(80));
    
    if (!isAdvancedLLMAvailable()) {
        console.log('❌ No DeepSeek-R1 API keys configured');
        console.log('   Please set up FIREWORKS_API_KEY, NOVITA_API_KEY, or OPENROUTER_API_KEY');
        return;
    }

    let totalTests = 0;
    let successfulTransformations = 0;
    let dramaticChanges = 0;

    for (const testCase of ULTRA_AGGRESSIVE_TEST_CASES) {
        totalTests++;
        console.log(`\n🧪 TEST ${totalTests}: ${testCase.name} (${testCase.difficulty})`);
        console.log('═'.repeat(60));
        
        console.log('\n📄 ORIGINAL TEXT:');
        console.log(`"${testCase.originalText}"`);
        console.log(`\n🎯 Target: ≤${testCase.targetDetection}% AI detection`);
        console.log(`📈 Expected: ${testCase.expectedTransformation}`);
        
        try {
            // Test with MAXIMUM aggressiveness
            const result = await humanizeWithAdvancedLLM(testCase.originalText, {
                aggressiveness: 1.0, // MAXIMUM
                maintainTone: false, // Don't maintain formal tone
                targetDetection: testCase.targetDetection,
                preferredModel: 'deepseek-r1',
                enableDeepThink: true
            });
            
            if (result.success) {
                successfulTransformations++;
                
                console.log('\n🎭 ULTRA-HUMANIZED OUTPUT:');
                console.log(`"${result.text}"`);
                
                // Analyze transformation quality
                const transformationAnalysis = analyzeTransformation(testCase.originalText, result.text);
                
                console.log('\n📊 TRANSFORMATION ANALYSIS:');
                console.log(`   Word Change Rate: ${transformationAnalysis.wordChangeRate.toFixed(1)}%`);
                console.log(`   Sentence Structure Change: ${transformationAnalysis.structureChange ? '✅ CHANGED' : '❌ SIMILAR'}`);
                console.log(`   Formal Language Eliminated: ${transformationAnalysis.formalLanguageEliminated ? '✅ YES' : '❌ NO'}`);
                console.log(`   Casual Language Added: ${transformationAnalysis.casualLanguageAdded ? '✅ YES' : '❌ NO'}`);
                console.log(`   Contractions Added: ${transformationAnalysis.contractionsAdded ? '✅ YES' : '❌ NO'}`);
                
                // Check for dramatic change
                if (transformationAnalysis.wordChangeRate >= 60 && transformationAnalysis.structureChange) {
                    dramaticChanges++;
                    console.log('🎉 DRAMATIC TRANSFORMATION ACHIEVED!');
                } else {
                    console.log('⚠️  Transformation not aggressive enough');
                }
                
                // DeepThink validation
                if (result.reasoningValidation) {
                    const rv = result.reasoningValidation;
                    console.log(`\n🧠 DEEPTHINK VALIDATION:`);
                    console.log(`   Reasoning Found: ${rv.hasReasoning ? '✅' : '❌'}`);
                    console.log(`   Reasoning Quality: ${(rv.reasoningQuality * 100).toFixed(1)}%`);
                    console.log(`   Reasoning Steps: ${rv.reasoningSteps}`);
                    
                    if (!rv.hasReasoning) {
                        console.log('⚠️  WARNING: DeepThink reasoning not found in response');
                    }
                }
                
                console.log(`\n⏱️  Processing Time: ${result.processingTime}ms`);
                console.log(`🔧 Model: ${result.provider}/${result.modelName || 'deepseek-r1'}`);
                
            } else {
                console.log(`\n❌ HUMANIZATION FAILED: ${result.error}`);
            }
            
        } catch (error) {
            console.log(`\n❌ TEST ERROR: ${error.message}`);
        }
        
        console.log('\n' + '─'.repeat(60));
    }
    
    // Final results
    console.log('\n\n🏆 FINAL RESULTS');
    console.log('═'.repeat(50));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Successful Transformations: ${successfulTransformations}/${totalTests} (${(successfulTransformations/totalTests*100).toFixed(1)}%)`);
    console.log(`Dramatic Changes: ${dramaticChanges}/${totalTests} (${(dramaticChanges/totalTests*100).toFixed(1)}%)`);
    
    if (dramaticChanges >= totalTests * 0.8) {
        console.log('🎉 EXCELLENT: Ultra-aggressive humanization is working!');
    } else if (dramaticChanges >= totalTests * 0.5) {
        console.log('✅ GOOD: Decent transformation rate, but could be more aggressive');
    } else {
        console.log('❌ POOR: Transformations not aggressive enough - needs improvement');
    }
}

/**
 * Analyze the quality of transformation between original and humanized text
 */
function analyzeTransformation(original, humanized) {
    const originalWords = original.toLowerCase().split(/\s+/);
    const humanizedWords = humanized.toLowerCase().split(/\s+/);
    
    // Calculate word change rate
    const commonWords = originalWords.filter(word => humanizedWords.includes(word));
    const wordChangeRate = ((originalWords.length - commonWords.length) / originalWords.length) * 100;
    
    // Check for structure change
    const originalSentences = original.split(/[.!?]+/).length;
    const humanizedSentences = humanized.split(/[.!?]+/).length;
    const structureChange = Math.abs(originalSentences - humanizedSentences) > 1;
    
    // Check for formal language elimination
    const formalPatterns = ['furthermore', 'moreover', 'additionally', 'consequently', 'therefore', 'thus', 'implementation', 'optimization', 'comprehensive'];
    const formalLanguageEliminated = !formalPatterns.some(pattern => humanized.toLowerCase().includes(pattern));
    
    // Check for casual language addition
    const casualPatterns = ['don\'t', 'won\'t', 'can\'t', 'it\'s', 'that\'s', 'pretty', 'really', 'basically', 'sort of', 'kind of'];
    const casualLanguageAdded = casualPatterns.some(pattern => humanized.toLowerCase().includes(pattern));
    
    // Check for contractions
    const contractionPatterns = ['\'t', '\'s', '\'re', '\'ve', '\'ll'];
    const contractionsAdded = contractionPatterns.some(pattern => humanized.includes(pattern));
    
    return {
        wordChangeRate,
        structureChange,
        formalLanguageEliminated,
        casualLanguageAdded,
        contractionsAdded
    };
}

/**
 * Test the public API with ultra-aggressive settings
 */
async function testPublicAPIUltraAggressive() {
    console.log('\n\n🌐 PUBLIC API ULTRA-AGGRESSIVE TEST');
    console.log('═'.repeat(60));
    
    const testText = "The implementation of artificial intelligence systems requires comprehensive analysis and systematic evaluation of all relevant factors.";
    
    console.log('📄 Original Text:');
    console.log(`"${testText}"`);
    
    try {
        const result = await humanizeText(testText, {
            aggressiveness: 1.0, // MAXIMUM
            maintainTone: false,
            targetDetection: 5, // Ultra-strict
            method: 'llm'
        });
        
        if (result.success) {
            console.log('\n🎭 Ultra-Humanized Result:');
            console.log(`"${result.humanizedText}"`);
            
            const analysis = analyzeTransformation(testText, result.humanizedText);
            console.log(`\n📊 Change Rate: ${analysis.wordChangeRate.toFixed(1)}%`);
            console.log(`🔧 Method: ${result.actualMethod}`);
            console.log(`⏱️  Time: ${result.totalProcessingTime}ms`);
            
            if (analysis.wordChangeRate >= 70) {
                console.log('🎉 EXCELLENT: Public API achieving ultra-aggressive transformations!');
            } else {
                console.log('⚠️  WARNING: Public API not aggressive enough');
            }
        } else {
            console.log(`❌ Public API Failed: ${result.error}`);
        }
    } catch (error) {
        console.log(`❌ Public API Error: ${error.message}`);
    }
}

// Run the ultra-aggressive test suite
async function runUltraAggressiveTests() {
    try {
        await testUltraAggressiveHumanization();
        await testPublicAPIUltraAggressive();
        
        console.log('\n\n🎯 VALIDATION COMPLETE');
        console.log('═'.repeat(60));
        console.log('For successful ultra-aggressive humanization, you should see:');
        console.log('✅ 60%+ word change rates');
        console.log('✅ Complete elimination of formal language');
        console.log('✅ Addition of casual language and contractions');
        console.log('✅ Dramatic sentence structure changes');
        console.log('✅ DeepThink reasoning chains in responses');
        console.log('\n🔍 Test the output with ZeroGPT to validate low AI detection scores!');
        
    } catch (error) {
        console.error('❌ Test suite error:', error.message);
    }
}

runUltraAggressiveTests().catch(console.error);
