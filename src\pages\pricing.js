// src/pages/pricing.js
import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '../components/layout/Layout';
import styles from '../styles/PricingPage.module.css';
import getStripePromise from '../../lib/stripe-client'; // Import getStripePromise

export default function PricingPage() {
    const { data: session, status: sessionStatus } = useSession();
    const router = useRouter();
    const isLoadingSession = sessionStatus === 'loading';

    const [userProfile, setUserProfile] = useState(null);
    const [isProfileLoading, setIsProfileLoading] = useState(false);
    const [upgradeError, setUpgradeError] = useState('');
    const [isProcessingUpgradeFor, setIsProcessingUpgradeFor] = useState(null); // Store priceId being processed

    // Fetch user profile to know current subscription status
    useEffect(() => {
        if (session?.user?.id && !userProfile && !isProfileLoading) {
            setIsProfileLoading(true);
            setUpgradeError(''); // Clear previous errors
            fetch('/api/user-profile')
                .then(async (res) => {
                    if (!res.ok) {
                        const errorData = await res.json().catch(() => ({}));
                        throw new Error(errorData.message || errorData.error || `Error ${res.status}: Failed to load user profile.`);
                    }
                    return res.json();
                })
                .then(data => setUserProfile(data))
                .catch(err => {
                    console.error("Failed to fetch user profile for pricing page:", err);
                    setUpgradeError(`Could not load your profile: ${err.message}. Please try refreshing.`);
                })
                .finally(() => setIsProfileLoading(false));
        }
    }, [session, userProfile, isProfileLoading]);

    const handleUpgradeClick = async (priceId) => {
        if (!session) {
            router.push(`/api/auth/signin?callbackUrl=${encodeURIComponent('/pricing')}`);
            return;
        }

        if (!priceId) {
            setUpgradeError('The selected plan is not available at the moment. Please try again later.');
            console.error("handleUpgradeClick: priceId is missing.");
            return;
        }

        setIsProcessingUpgradeFor(priceId);
        setUpgradeError('');

        try {
            // 1. Call backend to create Stripe Checkout session
            const res = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ priceId: priceId }),
            });

            if (!res.ok) {
                const errorData = await res.json().catch(() => ({ message: 'Failed to initiate checkout. Please try again.' }));
                throw new Error(errorData.message || errorData.error || `Error: ${res.status}`);
            }

            const { sessionId } = await res.json();
            if (!sessionId) {
                throw new Error('Checkout session ID not received from server.');
            }

            // 2. Redirect to Stripe Checkout using Stripe.js
            const stripe = await getStripePromise();
            if (!stripe) {
                throw new Error('Stripe.js failed to load. Please check your internet connection or ad-blocker.');
            }

            const { error: stripeError } = await stripe.redirectToCheckout({ sessionId });

            if (stripeError) {
                // This error is usually a client-side issue (e.g., network, Stripe.js misconfiguration)
                // or if Stripe itself can't process the redirect.
                console.error("Stripe redirectToCheckout error:", stripeError.message);
                setUpgradeError(`Error redirecting to checkout: ${stripeError.message}`);
            }
            // If redirectToCheckout is successful, the user is navigated away.
            // If it fails, the error is caught and displayed by setUpgradeError.

        } catch (error) {
            console.error('Upgrade process error:', error.message);
            setUpgradeError(`An error occurred: ${error.message}`);
        } finally {
            setIsProcessingUpgradeFor(null); // Clear loading state for this specific priceId
        }
    };

    const handleManageSubscriptionClick = async () => {
        setIsProcessingUpgradeFor('manage'); // Use a generic key or specific if multiple manage buttons
        setUpgradeError('');
        try {
            const res = await fetch('/api/stripe/create-customer-portal-session', { method: 'POST' });
            if (!res.ok) {
                const errorData = await res.json().catch(() => ({}));
                throw new Error(errorData.message || errorData.error || `Error ${res.status}`);
            }
            const { url } = await res.json();
            if (url) {
                window.location.href = url;
            } else {
                throw new Error('Customer portal URL not received.');
            }
        } catch (error) {
            console.error('Manage subscription error:', error.message);
            setUpgradeError(`Could not open subscription management: ${error.message}`);
        } finally {
            setIsProcessingUpgradeFor(null);
        }
    };

    // Determine current plan from userProfile or session as a fallback
    const currentSubscriptionTier = userProfile?.subscriptionTier || session?.user?.subscriptionTier || 'free';
    const premiumMonthlyPriceId = process.env.NEXT_PUBLIC_PREMIUM_PLAN_PRICE_ID || null;

    if (isLoadingSession || (session && isProfileLoading && !userProfile && !upgradeError)) { // Avoid hiding error if profile load fails
        return <Layout><div className={styles.loadingMessage}>Loading user data...</div></Layout>;
    }

    const plans = [
        {
            name: "Freemium",
            price: "$0",
            pricePer: "/ month",
            features: [
                "Basic paraphrasing quality",
                "Limited daily usage (e.g., 5-10 requests)",
                "Shorter input text length (e.g., 300-500 words)",
                "Standard processing speed",
                "Ad-supported experience",
                "Community support"
            ],
            tierIdentifier: "free",
        },
        {
            name: "Premium",
            price: "$9.99", // Example price, should match your Stripe Price object
            pricePer: "/ month",
            stripePriceId: premiumMonthlyPriceId,
            features: [
                "Advanced paraphrasing (PEGASUS model)",
                "Significantly higher daily usage (e.g., 100-200 requests)",
                "Longer input text length (e.g., 1500-3000 words)",
                "Priority processing (conceptual)",
                "Ad-free experience",
                "More fine-grained controls (future)",
                "Access to processing history (future)",
                "Priority email support (future)"
            ],
            tierIdentifier: "premium", // This should match a value used in User.subscriptionTier
        }
    ];

    return (
        <Layout>
            <div className={styles.pricingContainer}>
                <h1 className={styles.pageTitle}>Choose Your Plan</h1>
                <p className={styles.pageSubtitle}>
                    Supercharge your writing with our Premium features, higher limits, and an ad-free experience.
                </p>

                {upgradeError && <p className={styles.errorMessage}>{upgradeError}</p>}

                <div className={styles.plansGrid}>
                    {plans.map((plan) => {
                        const isCurrentPlan = currentSubscriptionTier === plan.tierIdentifier;
                        const isProcessingThisPlan = isProcessingUpgradeFor === plan.stripePriceId || (isCurrentPlan && isProcessingUpgradeFor === 'manage');
                        let buttonText = "Choose Plan";
                        let buttonAction = () => {};
                        let buttonDisabled = isProcessingUpgradeFor !== null; // Disable all buttons if any upgrade is processing

                        if (plan.tierIdentifier === "free") {
                            if (isCurrentPlan) {
                                buttonText = "Your Current Plan";
                                buttonDisabled = true;
                            } else if (!session) {
                                buttonText = "Sign Up for Free";
                                buttonAction = () => router.push(`/api/auth/signin?callbackUrl=${encodeURIComponent('/pricing')}`);
                            } else { // User is logged in but on a different (e.g. premium) plan
                                buttonText = "Downgrade (via Portal)"; // Or simply hide, or "Manage Subscription"
                                buttonAction = handleManageSubscriptionClick; // Downgrading is usually done via portal
                            }
                        } else { // Paid plans
                            if (isCurrentPlan) {
                                buttonText = "Manage Subscription";
                                buttonAction = handleManageSubscriptionClick;
                            } else {
                                buttonText = `Upgrade to ${plan.name}`;
                                buttonAction = () => handleUpgradeClick(plan.stripePriceId);
                                if (!plan.stripePriceId) buttonDisabled = true; // Disable if no price ID
                            }
                        }

                        if (isProcessingThisPlan) buttonText = "Processing...";

                        return (
                            <div key={plan.name} className={`${styles.planCard} ${isCurrentPlan ? styles.currentPlan : ''}`}>
                                <h2 className={styles.planTitle}>{plan.name}</h2>
                                <p className={styles.planPrice}>{plan.price} <span className={styles.pricePer}>{plan.pricePer}</span></p>
                                <ul className={styles.featuresList}>
                                    {plan.features.map((feature, index) => <li key={index}>{feature}</li>)}
                                </ul>
                                <button
                                    onClick={buttonAction}
                                    className={`${styles.planButton} ${plan.tierIdentifier !== 'free' && !isCurrentPlan ? styles.upgradeButton : ''}`}
                                    disabled={buttonDisabled}
                                >
                                    {buttonText}
                                </button>
                                {plan.tierIdentifier !== 'free' && !plan.stripePriceId &&
                                    <p className={styles.warningMessage}>This plan is not available for purchase at this moment. Please check back later.</p>
                                }
                            </div>
                        );
                    })}
                </div>
                <p style={{marginTop: '2rem', fontSize: '0.9rem', color: '#777'}}>
                    Payments are securely processed by Stripe. You can manage your subscription at any time.
                </p>
            </div>
        </Layout>
    );
}
