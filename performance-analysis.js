/**
 * Performance Analysis Tool for NLTK-inspired Humanization
 * Identifies bottlenecks and measures processing times for optimization
 */

import { humanizeWithNLTKApproach } from './src/services/falconService.js';

// Test texts of varying lengths and complexity
const testTexts = [
    // Short text (50 words)
    "This is a very good example of how the advanced system works effectively. The results are quite impressive and show significant improvements in processing capabilities.",
    
    // Medium text (150 words)
    "The advanced algorithm demonstrates remarkable performance in processing large datasets efficiently. It handles complex operations while maintaining high accuracy levels throughout the entire process. Furthermore, the implementation reveals substantial benefits in terms of computational efficiency and scalability. The system exhibits excellent characteristics that make it particularly suitable for enterprise applications requiring robust performance metrics.",
    
    // Long text (300 words)
    "The comprehensive framework provides exceptional functionality for data processing and analysis across multiple domains. Users can easily integrate the service into their existing applications without significant modifications to their current infrastructure. The system offers several key advantages including high performance processing capabilities, excellent accuracy rates, and seamless integration features. These characteristics make it particularly suitable for enterprise applications that require robust and reliable performance metrics. Moreover, the advanced algorithms demonstrate remarkable efficiency in handling complex operations while maintaining consistent quality standards. The implementation reveals substantial benefits in terms of computational optimization and resource utilization. Additionally, the framework supports various input formats and provides flexible configuration options to meet diverse user requirements. The scalable architecture ensures optimal performance even under heavy workload conditions, making it an ideal solution for organizations seeking to enhance their data processing capabilities."
];

// Performance measurement utilities
class PerformanceProfiler {
    constructor() {
        this.measurements = {};
    }
    
    start(label) {
        this.measurements[label] = { start: performance.now() };
    }
    
    end(label) {
        if (this.measurements[label]) {
            this.measurements[label].end = performance.now();
            this.measurements[label].duration = this.measurements[label].end - this.measurements[label].start;
        }
    }
    
    getResults() {
        const results = {};
        for (const [label, data] of Object.entries(this.measurements)) {
            if (data.duration !== undefined) {
                results[label] = Math.round(data.duration * 100) / 100; // Round to 2 decimal places
            }
        }
        return results;
    }
    
    reset() {
        this.measurements = {};
    }
}

async function analyzePerformanceBottlenecks() {
    console.log('🔍 NLTK Humanization Performance Analysis');
    console.log('=' .repeat(60));
    
    const profiler = new PerformanceProfiler();
    
    for (let i = 0; i < testTexts.length; i++) {
        const text = testTexts[i];
        const wordCount = text.split(/\s+/).length;
        
        console.log(`\n📝 Test ${i + 1}: ${wordCount} words`);
        console.log('-'.repeat(40));
        
        // Test each user tier
        const tiers = ['free', 'premium', 'admin'];
        
        for (const tier of tiers) {
            console.log(`\n🔧 Testing ${tier.toUpperCase()} tier:`);
            
            profiler.reset();
            profiler.start('total');
            
            try {
                const result = await humanizeWithNLTKApproach(text, {
                    aggressiveness: 0.7,
                    userTier: tier
                });
                
                profiler.end('total');
                
                if (result.success) {
                    const results = profiler.getResults();
                    console.log(`⏱️  Total time: ${results.total}ms`);
                    console.log(`📊 Processing time: ${result.processingTime}ms`);
                    console.log(`🎯 User tier: ${result.userTier}`);
                    console.log(`📈 Words/second: ${Math.round(wordCount / (results.total / 1000))}`);
                    
                    // Calculate efficiency metrics
                    const efficiency = {
                        wordsPerMs: (wordCount / results.total).toFixed(3),
                        msPerWord: (results.total / wordCount).toFixed(3),
                        throughput: Math.round(wordCount / (results.total / 1000))
                    };
                    
                    console.log(`📋 Efficiency: ${efficiency.wordsPerMs} words/ms, ${efficiency.msPerWord} ms/word`);
                    
                    if (results.total > 1000) {
                        console.log('⚠️  SLOW PERFORMANCE DETECTED');
                    }
                } else {
                    console.log('❌ Failed:', result.error);
                }
            } catch (error) {
                console.error('💥 Error:', error.message);
            }
        }
    }
}

async function identifySpecificBottlenecks() {
    console.log('\n🎯 Specific Bottleneck Analysis');
    console.log('=' .repeat(60));
    
    const testText = testTexts[1]; // Medium complexity text
    const wordCount = testText.split(/\s+/).length;
    
    console.log(`Testing with ${wordCount} words...`);
    
    // Simulate bottleneck analysis by measuring different components
    const bottlenecks = [
        'Text preprocessing (regex operations)',
        'POS tagging (pattern matching)',
        'Synonym lookup (database queries)', 
        'Word replacement (probability calculations)',
        'Text reconstruction (string operations)',
        'Cache operations (if enabled)',
        'Parallel processing overhead'
    ];
    
    console.log('\n🔍 Identified potential bottlenecks:');
    bottlenecks.forEach((bottleneck, index) => {
        console.log(`${index + 1}. ${bottleneck}`);
    });
    
    // Test with different aggressiveness levels
    console.log('\n📊 Aggressiveness Impact Analysis:');
    const aggressivenessLevels = [0.3, 0.5, 0.7, 0.9];
    
    for (const aggressiveness of aggressivenessLevels) {
        const start = performance.now();
        
        try {
            const result = await humanizeWithNLTKApproach(testText, {
                aggressiveness,
                userTier: 'premium'
            });
            
            const duration = performance.now() - start;
            
            if (result.success) {
                console.log(`Aggressiveness ${aggressiveness}: ${Math.round(duration)}ms`);
            }
        } catch (error) {
            console.log(`Aggressiveness ${aggressiveness}: Error - ${error.message}`);
        }
    }
}

async function benchmarkCurrentPerformance() {
    console.log('\n📈 Current Performance Benchmark');
    console.log('=' .repeat(60));
    
    const iterations = 5;
    const testText = testTexts[1]; // Medium text
    const results = { free: [], premium: [], admin: [] };
    
    for (const tier of Object.keys(results)) {
        console.log(`\n🔄 Benchmarking ${tier.toUpperCase()} tier (${iterations} iterations):`);
        
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            
            try {
                const result = await humanizeWithNLTKApproach(testText, {
                    aggressiveness: 0.7,
                    userTier: tier
                });
                
                const duration = performance.now() - start;
                results[tier].push(duration);
                
                console.log(`  Run ${i + 1}: ${Math.round(duration)}ms`);
            } catch (error) {
                console.log(`  Run ${i + 1}: Error - ${error.message}`);
            }
        }
        
        // Calculate statistics
        if (results[tier].length > 0) {
            const avg = results[tier].reduce((a, b) => a + b, 0) / results[tier].length;
            const min = Math.min(...results[tier]);
            const max = Math.max(...results[tier]);
            
            console.log(`📊 ${tier.toUpperCase()} Stats: Avg=${Math.round(avg)}ms, Min=${Math.round(min)}ms, Max=${Math.round(max)}ms`);
        }
    }
    
    return results;
}

// Run comprehensive analysis
async function runCompleteAnalysis() {
    console.log('🚀 Starting Comprehensive Performance Analysis\n');
    
    try {
        await analyzePerformanceBottlenecks();
        await identifySpecificBottlenecks();
        const benchmarkResults = await benchmarkCurrentPerformance();
        
        console.log('\n✅ Analysis Complete!');
        console.log('\n📋 Summary of Findings:');
        console.log('1. Current performance bottlenecks identified');
        console.log('2. User tier performance differences measured');
        console.log('3. Text length impact analyzed');
        console.log('4. Aggressiveness level impact assessed');
        console.log('5. Baseline performance benchmarks established');
        
        return benchmarkResults;
    } catch (error) {
        console.error('💥 Analysis failed:', error);
    }
}

// Execute analysis
runCompleteAnalysis().catch(console.error);
