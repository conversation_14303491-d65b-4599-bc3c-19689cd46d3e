// src/pages/_app.js
import '../styles/globals.css'; // Import global styles
import { SessionProvider } from 'next-auth/react';

// This is the main App component that Next.js uses to initialize pages.
// Changes here will affect all pages in the application.

function MyApp({ Component, pageProps }) {
  // pageProps contains the initial props that were preloaded for your page by
  // one of Next.js's data fetching methods, otherwise it's an empty object.
  // `pageProps.session` is automatically populated by NextAuth.js if you are using server-side props
  // or the session is available through other means NextAuth.js provides.

  // If you had a global Layout component that should apply to all pages,
  // it would typically be integrated here. For example:
  // import Layout from '../components/layout/Layout';
  //
  // return (
  //   <SessionProvider session={pageProps.session}>
  //     <Layout>
  //       <Component {...pageProps} />
  //     </Layout>
  //   </SessionProvider>
  // );
  //
  // For the current structure, where Layout is applied within individual pages (e.g., HomePage),
  // we just wrap with SessionProvider.

  return (
    <SessionProvider session={pageProps.session}>
      <Component {...pageProps} />
    </SessionProvider>
  );
}

export default MyApp;
