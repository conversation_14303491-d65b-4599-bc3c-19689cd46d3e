// Comprehensive test of the restored humanization system
import { humanizeText } from './src/services/humaneyesService.js';

const testCases = [
    {
        name: "Academic Text",
        text: "Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for future applications. Furthermore, it is important to note that machine learning algorithms can effectively analyze vast amounts of data to identify patterns and make predictions."
    },
    {
        name: "Business Content", 
        text: "Organizations are increasingly leveraging advanced technologies to optimize their operations and enhance decision-making processes. Consequently, the implementation of these systems has resulted in substantial improvements in efficiency and accuracy across various sectors."
    },
    {
        name: "Technical Documentation",
        text: "The system utilizes sophisticated neural networks to process information in ways that mimic human cognitive processes. Additionally, these algorithms can effectively identify patterns and correlations within complex datasets to generate actionable insights."
    },
    {
        name: "Formal Report",
        text: "It should be noted that this technological evolution requires careful consideration of ethical implications and responsible development practices. Therefore, stakeholders must collaborate to ensure that artificial intelligence benefits society as a whole."
    },
    {
        name: "Short Content",
        text: "Machine learning represents a transformative shift in how businesses approach complex challenges."
    }
];

async function runComprehensiveTest() {
    console.log('🧪 Comprehensive Humanization Quality Test\n');
    console.log('Testing restored system for ≤10% AI detection target\n');
    console.log('='.repeat(80) + '\n');
    
    const results = [];
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📝 Test ${i + 1}: ${testCase.name}`);
        console.log('-'.repeat(50));
        console.log(`Original (${testCase.text.length} chars):`);
        console.log(testCase.text.substring(0, 100) + '...\n');
        
        try {
            // Test different methods
            const methods = [
                { name: 'Auto (Default)', method: 'auto', target: 10 },
                { name: 'Pattern-Based', method: 'pattern', target: 10 },
                { name: 'Commercial Grade', method: 'commercial', target: 5 }
            ];
            
            for (const methodTest of methods) {
                console.log(`🔬 Testing ${methodTest.name} (Target: ≤${methodTest.target}%):`);
                
                const startTime = Date.now();
                const result = await humanizeText(testCase.text, {
                    aggressiveness: 0.8,
                    maintainTone: true,
                    targetDetection: methodTest.target,
                    method: methodTest.method,
                    fallbackEnabled: true
                });
                const processingTime = Date.now() - startTime;
                
                if (result.success) {
                    const humanizedText = result.text || result.humanizedText;
                    const originalWords = testCase.text.toLowerCase().split(/\s+/);
                    const humanizedWords = humanizedText.toLowerCase().split(/\s+/);
                    
                    let changedWords = 0;
                    for (let j = 0; j < Math.min(originalWords.length, humanizedWords.length); j++) {
                        if (originalWords[j] !== humanizedWords[j]) {
                            changedWords++;
                        }
                    }
                    
                    const changePercentage = ((changedWords / originalWords.length) * 100).toFixed(1);
                    
                    // Quality assessment
                    let qualityRating = 'UNKNOWN';
                    if (changePercentage < 5) {
                        qualityRating = '❌ POOR';
                    } else if (changePercentage < 15) {
                        qualityRating = '⚠️  FAIR';
                    } else if (changePercentage < 30) {
                        qualityRating = '✅ GOOD';
                    } else {
                        qualityRating = '🎉 EXCELLENT';
                    }
                    
                    console.log(`   ✅ Success: ${result.actualMethod || result.method}`);
                    console.log(`   📊 Transformation: ${changePercentage}% (${changedWords}/${originalWords.length} words)`);
                    console.log(`   📈 Quality: ${qualityRating}`);
                    console.log(`   ⏱️  Time: ${processingTime}ms`);
                    console.log(`   📄 Length: ${testCase.text.length} → ${humanizedText.length} chars`);
                    
                    // Check for word shifting issues
                    let shiftingIssues = 0;
                    const maxCheck = Math.min(10, originalWords.length); // Check first 10 words
                    for (let k = 0; k < maxCheck; k++) {
                        if (originalWords[k] && humanizedWords[k] && 
                            originalWords[k] !== humanizedWords[k] && 
                            !isValidReplacement(originalWords[k], humanizedWords[k])) {
                            shiftingIssues++;
                        }
                    }
                    
                    if (shiftingIssues === 0) {
                        console.log(`   ✅ Word Alignment: Perfect (no shifting detected)`);
                    } else if (shiftingIssues <= 2) {
                        console.log(`   ⚠️  Word Alignment: Minor issues (${shiftingIssues} potential shifts)`);
                    } else {
                        console.log(`   ❌ Word Alignment: Major issues (${shiftingIssues} potential shifts)`);
                    }
                    
                    results.push({
                        testCase: testCase.name,
                        method: methodTest.name,
                        success: true,
                        changePercentage: parseFloat(changePercentage),
                        qualityRating,
                        processingTime,
                        shiftingIssues,
                        actualMethod: result.actualMethod || result.method
                    });
                    
                } else {
                    console.log(`   ❌ Failed: ${result.error}`);
                    results.push({
                        testCase: testCase.name,
                        method: methodTest.name,
                        success: false,
                        error: result.error
                    });
                }
                
                console.log('');
            }
            
        } catch (error) {
            console.error(`❌ Error testing ${testCase.name}:`, error.message);
        }
        
        console.log('='.repeat(80) + '\n');
    }
    
    // Summary
    console.log('📊 COMPREHENSIVE TEST SUMMARY');
    console.log('='.repeat(80));
    
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);
    
    console.log(`Total Tests: ${results.length}`);
    console.log(`Successful: ${successfulTests.length} (${((successfulTests.length / results.length) * 100).toFixed(1)}%)`);
    console.log(`Failed: ${failedTests.length} (${((failedTests.length / results.length) * 100).toFixed(1)}%)`);
    
    if (successfulTests.length > 0) {
        const avgTransformation = (successfulTests.reduce((sum, r) => sum + r.changePercentage, 0) / successfulTests.length).toFixed(1);
        const avgProcessingTime = Math.round(successfulTests.reduce((sum, r) => sum + r.processingTime, 0) / successfulTests.length);
        
        console.log(`\nPerformance Metrics:`);
        console.log(`Average Transformation: ${avgTransformation}%`);
        console.log(`Average Processing Time: ${avgProcessingTime}ms`);
        
        // Quality distribution
        const qualityDistribution = {};
        successfulTests.forEach(r => {
            const quality = r.qualityRating.split(' ')[1] || r.qualityRating;
            qualityDistribution[quality] = (qualityDistribution[quality] || 0) + 1;
        });
        
        console.log(`\nQuality Distribution:`);
        Object.entries(qualityDistribution).forEach(([quality, count]) => {
            console.log(`${quality}: ${count} tests (${((count / successfulTests.length) * 100).toFixed(1)}%)`);
        });
        
        // Word shifting analysis
        const noShifting = successfulTests.filter(r => r.shiftingIssues === 0).length;
        const minorShifting = successfulTests.filter(r => r.shiftingIssues > 0 && r.shiftingIssues <= 2).length;
        const majorShifting = successfulTests.filter(r => r.shiftingIssues > 2).length;
        
        console.log(`\nWord Alignment Analysis:`);
        console.log(`Perfect Alignment: ${noShifting} tests (${((noShifting / successfulTests.length) * 100).toFixed(1)}%)`);
        console.log(`Minor Issues: ${minorShifting} tests (${((minorShifting / successfulTests.length) * 100).toFixed(1)}%)`);
        console.log(`Major Issues: ${majorShifting} tests (${((majorShifting / successfulTests.length) * 100).toFixed(1)}%)`);
    }
    
    console.log('\n🎯 CONCLUSION:');
    if (successfulTests.length >= results.length * 0.8) {
        console.log('✅ System is performing well and ready for production use');
    } else if (successfulTests.length >= results.length * 0.6) {
        console.log('⚠️  System has some issues but is functional');
    } else {
        console.log('❌ System needs significant improvements');
    }
}

function isValidReplacement(original, replacement) {
    // Check if this is a valid synonym replacement rather than word shifting
    const synonymPairs = [
        ['demonstrate', 'show'], ['significant', 'major'], ['furthermore', 'plus'],
        ['utilize', 'use'], ['implement', 'put'], ['facilitate', 'help'],
        ['optimize', 'improve'], ['substantial', 'major'], ['numerous', 'many']
    ];
    
    return synonymPairs.some(([word1, word2]) => 
        (original === word1 && replacement === word2) || 
        (original === word2 && replacement === word1)
    );
}

// Run the test
runComprehensiveTest().catch(console.error);
