// src/pages/api/stripe/webhook.js
import { buffer } from 'micro'; // Helper to read the raw request body
import stripe from '../../../lib/stripe'; // Your initialized Stripe instance
import prisma from '../../../lib/prisma'; // Your initialized Prisma instance

// Disable Next.js body parsing for this route; Stripe requires the raw body for signature verification.
export const config = {
    api: {
        bodyParser: false,
    },
};

// Helper function to safely get string value, defaulting to empty string if not a string.
// This is important because Stripe IDs should always be strings for DB operations.
const ensureString = (value) => (typeof value === 'string' ? value : '');


export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    const rawBody = await buffer(req);
    const sig = req.headers['stripe-signature'];
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
        if (!sig || !webhookSecret) {
            console.error("Stripe Webhook Error: Missing stripe-signature or webhook secret.");
            return res.status(400).send('Webhook Error: Missing signature or secret.');
        }
        event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);
    } catch (err) {
        console.error(`Stripe Webhook signature verification failed: ${err.message}`, err);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    console.log(`Received Stripe event: type: ${event.type}, id: ${event.id}`);

    // Handle the specific event type
    try {
        switch (event.type) {
            case 'checkout.session.completed':
                const checkoutSession = event.data.object;
                console.log(`Processing checkout.session.completed for session ID: ${checkoutSession.id}`);

                const userId = checkoutSession.client_reference_id; // Your internal user ID
                const stripeSubscriptionId = ensureString(checkoutSession.subscription);
                const stripeCustomerId = ensureString(checkoutSession.customer);

                if (!userId) {
                    console.error("Webhook Error (checkout.session.completed): Missing client_reference_id (userId).");
                    return res.status(400).json({ error: "Webhook Error: Missing client_reference_id." });
                }
                if (!stripeSubscriptionId) {
                    console.error("Webhook Error (checkout.session.completed): Missing subscription ID in session.");
                    return res.status(400).json({ error: "Webhook Error: Missing subscription ID." });
                }
                 if (!stripeCustomerId) {
                    console.error("Webhook Error (checkout.session.completed): Missing customer ID in session.");
                    return res.status(400).json({ error: "Webhook Error: Missing customer ID." });
                }

                // Retrieve full subscription details from Stripe to get all necessary fields
                const subscriptionDetails = await stripe.subscriptions.retrieve(stripeSubscriptionId);
                const stripePriceId = ensureString(subscriptionDetails.items.data[0]?.price.id);
                const stripeCurrentPeriodEnd = new Date(subscriptionDetails.current_period_end * 1000);
                const subscriptionStatus = subscriptionDetails.status; // e.g., 'active', 'trialing'

                // Idempotency: Check if we've already processed this subscription
                const existingSubscription = await prisma.subscription.findUnique({
                    where: { stripeSubscriptionId: stripeSubscriptionId },
                });

                if (existingSubscription) {
                    console.log(`Subscription ${stripeSubscriptionId} already processed. Skipping creation.`);
                } else {
                    // Create Subscription record in your database
                    await prisma.subscription.create({
                        data: {
                            userId: userId,
                            stripeSubscriptionId: stripeSubscriptionId,
                            stripeCustomerId: stripeCustomerId,
                            stripePriceId: stripePriceId,
                            status: subscriptionStatus,
                            stripeCurrentPeriodEnd: stripeCurrentPeriodEnd,
                        },
                    });
                     console.log(`Subscription record created for ${stripeSubscriptionId}, user ${userId}.`);
                }

                // Update User record
                // TODO: Determine premium tier name based on stripePriceId if you have multiple plans
                const premiumTierName = 'premium'; // Example
                await prisma.user.update({
                    where: { id: userId },
                    data: {
                        subscriptionTier: premiumTierName,
                        usageCredits: 1000, // Example: Reset/set to premium credits
                        stripeCustomerId: stripeCustomerId, // Ensure it's stored on user record
                    },
                });
                console.log(`User ${userId} updated to tier '${premiumTierName}'.`);
                break;

            case 'invoice.payment_succeeded':
                const invoice = event.data.object;
                console.log(`Processing invoice.payment_succeeded for invoice ID: ${invoice.id}, subscription: ${invoice.subscription}`);

                const invSubId = ensureString(invoice.subscription);
                if (!invSubId) {
                    console.error("Webhook Error (invoice.payment_succeeded): Missing subscription ID in invoice.");
                    return res.status(400).json({ error: "Webhook Error: Missing subscription ID." });
                }

                // Retrieve the subscription to get the latest details, especially current_period_end and priceId
                const invSubDetails = await stripe.subscriptions.retrieve(invSubId);
                const invPriceId = ensureString(invSubDetails.items.data[0]?.price.id);
                const invCurrentPeriodEnd = new Date(invSubDetails.current_period_end * 1000);
                const invSubStatus = invSubDetails.status;

                const updatedDbSubscription = await prisma.subscription.update({
                    where: { stripeSubscriptionId: invSubId },
                    data: {
                        status: invSubStatus, // Should be 'active'
                        stripePriceId: invPriceId,
                        stripeCurrentPeriodEnd: invCurrentPeriodEnd,
                    },
                });

                // Update user's tier and reset credits for the new period
                // TODO: Determine premium tier name based on stripePriceId
                const userPremiumTierName = 'premium'; // Example
                await prisma.user.update({
                    where: { id: updatedDbSubscription.userId },
                    data: {
                        subscriptionTier: userPremiumTierName,
                        usageCredits: 1000, // Reset credits
                    },
                });
                console.log(`Subscription ${invSubId} renewed for user ${updatedDbSubscription.userId}.`);
                break;

            case 'invoice.payment_failed':
                const failedInvoice = event.data.object;
                console.log(`Processing invoice.payment_failed for invoice ID: ${failedInvoice.id}, subscription: ${failedInvoice.subscription}`);
                const failedInvSubId = ensureString(failedInvoice.subscription);

                if (failedInvSubId) {
                    await prisma.subscription.update({
                        where: { stripeSubscriptionId: failedInvSubId },
                        data: { status: 'past_due' }, // Or 'unpaid', consult Stripe docs for dunning flow
                    });
                    // Optionally, update user tier immediately or trigger a grace period logic
                    // const subRecord = await prisma.subscription.findUnique({ where: {stripeSubscriptionId: failedInvSubId }});
                    // if (subRecord) {
                    //    await prisma.user.update({ where: {id: subRecord.userId }, data: { subscriptionTier: 'free_grace_period' }});
                    // }
                    console.log(`Subscription ${failedInvSubId} status updated to 'past_due'.`);
                } else {
                     console.error("Webhook Error (invoice.payment_failed): Missing subscription ID.");
                }
                break;

            case 'customer.subscription.updated':
                const updatedSubEvent = event.data.object;
                console.log(`Processing customer.subscription.updated for subscription ID: ${updatedSubEvent.id}, status: ${updatedSubEvent.status}`);

                const subIdToUpdate = ensureString(updatedSubEvent.id);
                const newStatus = updatedSubEvent.status; // e.g., 'active', 'trialing', 'past_due', 'canceled'
                const newPriceId = ensureString(updatedSubEvent.items.data[0]?.price.id);
                const newPeriodEnd = new Date(updatedSubEvent.current_period_end * 1000);
                // cancel_at_period_end is true if user chose to cancel, but sub is active until period_end
                const cancelAtPeriodEnd = updatedSubEvent.cancel_at_period_end;

                let userTier = 'free'; // Default if subscription ends or becomes problematic
                if (newStatus === 'active' || newStatus === 'trialing') {
                    // TODO: Determine premium tier name based on newPriceId
                    userTier = 'premium';
                } else if (newStatus === 'past_due' && !cancelAtPeriodEnd) {
                     userTier = 'premium_grace_period'; // Example custom status
                }


                const dbSubUpdated = await prisma.subscription.update({
                    where: { stripeSubscriptionId: subIdToUpdate },
                    data: {
                        status: newStatus,
                        stripePriceId: newPriceId,
                        stripeCurrentPeriodEnd: newPeriodEnd,
                    },
                });

                if (dbSubUpdated) {
                    await prisma.user.update({
                        where: { id: dbSubUpdated.userId },
                        data: { subscriptionTier: userTier }, // Update user's access tier
                    });
                    console.log(`Subscription ${subIdToUpdate} status updated to ${newStatus}, user ${dbSubUpdated.userId} tier set to ${userTier}.`);
                } else {
                     console.warn(`Subscription ${subIdToUpdate} not found in DB during customer.subscription.updated.`);
                }
                break;

            case 'customer.subscription.deleted': // Occurs when a subscription is canceled definitively
                const deletedSubEvent = event.data.object;
                console.log(`Processing customer.subscription.deleted for subscription ID: ${deletedSubEvent.id}`);
                const subIdToDelete = ensureString(deletedSubEvent.id);

                const dbSubDeleted = await prisma.subscription.update({
                    where: { stripeSubscriptionId: subIdToDelete },
                    data: { status: 'canceled' }, // Or 'ended', 'expired'
                });

                if (dbSubDeleted) {
                    await prisma.user.update({
                        where: { id: dbSubDeleted.userId },
                        data: { subscriptionTier: 'free', usageCredits: 10 }, // Downgrade user to free tier limits
                    });
                    console.log(`Subscription ${subIdToDelete} marked as 'canceled' for user ${dbSubDeleted.userId}, user tier set to 'free'.`);
                } else {
                    console.warn(`Subscription ${subIdToDelete} not found in DB during customer.subscription.deleted.`);
                }
                break;

            // Add other event types to handle as needed:
            // - customer.subscription.trial_will_end: Notify user about upcoming trial end.
            // - etc.

            default:
                console.warn(`Unhandled Stripe event type: ${event.type}`);
        }
    } catch (error) {
        console.error(`Error processing Stripe webhook event ${event.type} (ID: ${event.id}): ${error.message}`, error);
        // Do not send detailed error to Stripe, just a generic failure,
        // unless Stripe specifically asks for certain error types.
        return res.status(500).json({ error: "Webhook handler failed. See server logs." });
    }

    // Return a 200 response to Stripe to acknowledge receipt of the event
    res.status(200).json({ received: true });
}
