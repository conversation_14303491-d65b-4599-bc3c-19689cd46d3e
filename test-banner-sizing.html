<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner Sizing Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .screen-size-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1001;
        }
        
        .text-editor-mockup {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .editor-box {
            background: #f9f9f9;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
        }
        
        .editor-box h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .editor-box textarea {
            width: 100%;
            height: 250px;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            resize: vertical;
        }
        
        /* Mock floating banner styles */
        .mock-floating-banner {
            position: fixed;
            bottom: 15px;
            right: 15px;
            max-width: 240px;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            z-index: 999;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .mock-floating-banner .emoji {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
            text-align: center;
        }
        
        .mock-floating-banner .title {
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0 0 0.25rem 0;
            line-height: 1.2;
            text-align: center;
        }
        
        .mock-floating-banner .subtitle {
            font-size: 0.75rem;
            margin: 0 0 0.5rem 0;
            line-height: 1.3;
            opacity: 0.9;
            text-align: center;
        }
        
        .mock-floating-banner .button {
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 6px;
            width: 100%;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            cursor: pointer;
            text-align: center;
        }
        
        /* Responsive styles matching the actual implementation */
        @media (min-width: 1400px) {
            .mock-floating-banner {
                max-width: 260px;
                padding: 0.85rem;
                bottom: 20px;
                right: 20px;
            }
            .mock-floating-banner .title { font-size: 0.95rem; }
            .mock-floating-banner .subtitle { font-size: 0.78rem; }
        }
        
        @media (max-width: 1024px) and (min-width: 769px) {
            .mock-floating-banner {
                max-width: 220px;
                padding: 0.6rem;
                bottom: 12px;
                right: 12px;
            }
            .mock-floating-banner .title { font-size: 0.85rem; }
            .mock-floating-banner .subtitle { font-size: 0.72rem; }
            .mock-floating-banner .button { padding: 0.38rem 0.7rem; font-size: 0.72rem; }
        }
        
        @media (max-width: 768px) {
            .text-editor-mockup {
                grid-template-columns: 1fr;
            }
            .mock-floating-banner {
                max-width: 200px;
                padding: 0.5rem;
                bottom: 10px;
                right: 10px;
            }
            .mock-floating-banner .emoji { font-size: 1.2rem; }
            .mock-floating-banner .title { font-size: 0.8rem; line-height: 1.1; }
            .mock-floating-banner .subtitle { font-size: 0.7rem; line-height: 1.2; }
            .mock-floating-banner .button { padding: 0.35rem 0.6rem; font-size: 0.7rem; }
        }
    </style>
</head>
<body>
    <div class="screen-size-info" id="screenInfo">
        Screen: <span id="screenSize"></span>
    </div>
    
    <div class="test-container">
        <h1>Floating Banner Sizing Test</h1>
        <p>This page tests the compact floating banner design across different screen sizes.</p>
        
        <div class="text-editor-mockup">
            <div class="editor-box">
                <h3>📝 Input Text</h3>
                <textarea placeholder="Paste your AI-generated text here...">This is a sample AI-generated text that needs to be humanized. The system will process this text and make it undetectable by AI detection tools.</textarea>
            </div>
            
            <div class="editor-box">
                <h3>✨ Humanized Text</h3>
                <textarea placeholder="Humanized text will appear here...">This represents a sample AI-generated text that requires humanization. The framework will analyze this content and render it undetectable by AI detection systems.</textarea>
            </div>
        </div>
        
        <div style="height: 400px; background: #f0f0f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3>Additional Content Area</h3>
            <p>This simulates additional page content to test banner positioning and overlap prevention.</p>
            <p>The floating banner should remain visible and accessible without interfering with this content.</p>
        </div>
    </div>
    
    <!-- Mock floating banner -->
    <div class="mock-floating-banner">
        <div class="emoji">🚀</div>
        <div class="title">95% Bypass Rate</div>
        <div class="subtitle">Join the AI humanization revolution</div>
        <button class="button">Try Free</button>
    </div>
    
    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let category = '';
            
            if (width >= 1400) category = 'Large Desktop';
            else if (width >= 1025) category = 'Desktop';
            else if (width >= 769) category = 'Tablet';
            else category = 'Mobile';
            
            document.getElementById('screenSize').textContent = `${width}x${height} (${category})`;
        }
        
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
