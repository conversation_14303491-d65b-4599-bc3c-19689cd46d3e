/**
 * GPTZero Client - Mock Implementation for Testing
 * This is a mock implementation for testing purposes when GPTZero API is not available
 */

/**
 * Mock AI detection function that simulates GPTZero API responses
 * @param {string} text - Text to analyze for AI detection
 * @returns {Promise<Object>} Detection result object
 */
export async function checkWithGPTZero(text) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            error: true,
            message: 'Input text is required and must be a non-empty string',
            status: 'error'
        };
    }

    // Mock AI detection logic based on text patterns
    const aiScore = calculateMockAIScore(text);
    
    return {
        error: false,
        status: 'success',
        ai_probability: aiScore / 100, // GPTZero returns probability as decimal
        score: aiScore,
        message: 'Mock detection completed successfully',
        mock: true // Indicate this is a mock response
    };
}

/**
 * Calculate a mock AI detection score based on text characteristics
 * @param {string} text - Text to analyze
 * @returns {number} AI detection score (0-100)
 */
function calculateMockAIScore(text) {
    let score = 50; // Base score
    
    // Factors that increase AI detection score
    const aiPatterns = [
        /\b(furthermore|moreover|additionally|consequently|therefore)\b/gi,
        /\b(demonstrates|exhibits|reveals|indicates|suggests)\b/gi,
        /\b(comprehensive|substantial|significant|remarkable|exceptional)\b/gi,
        /\b(optimize|enhance|facilitate|implement|utilize)\b/gi,
        /\b(artificial intelligence|machine learning|deep learning)\b/gi
    ];
    
    // Count AI-typical patterns
    let patternCount = 0;
    aiPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
            patternCount += matches.length;
        }
    });
    
    // Adjust score based on patterns
    score += Math.min(patternCount * 3, 30);
    
    // Factors that decrease AI detection score (more human-like)
    const humanPatterns = [
        /\b(I think|I believe|in my opinion|personally)\b/gi,
        /\b(um|uh|well|you know|like)\b/gi,
        /[!]{2,}|[?]{2,}/g, // Multiple punctuation
        /\b(gonna|wanna|kinda|sorta)\b/gi, // Contractions
        /\.\.\./g // Ellipses
    ];
    
    let humanCount = 0;
    humanPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
            humanCount += matches.length;
        }
    });
    
    // Reduce score for human-like patterns
    score -= Math.min(humanCount * 5, 25);
    
    // Sentence length variation (AI tends to have consistent length)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length > 1) {
        const lengths = sentences.map(s => s.trim().split(/\s+/).length);
        const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
        const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
        
        // Higher variance = more human-like
        if (variance > 50) {
            score -= 10;
        } else if (variance < 10) {
            score += 10;
        }
    }
    
    // Ensure score is within bounds
    return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * Test the GPTZero client connection (mock version)
 * @returns {Promise<Object>} Connection test result
 */
export async function testGPTZeroConnection() {
    return {
        success: true,
        message: 'Mock GPTZero client is available',
        mock: true
    };
}

/**
 * Get GPTZero service status (mock version)
 * @returns {Object} Service status
 */
export function getGPTZeroStatus() {
    return {
        available: true,
        mock: true,
        message: 'Mock GPTZero service is running'
    };
}

export default {
    checkWithGPTZero,
    testGPTZeroConnection,
    getGPTZeroStatus
};
